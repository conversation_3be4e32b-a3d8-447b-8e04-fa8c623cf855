# 🦊 SecureFox - 智能密码管理器

基于领域驱动设计(DDD)架构的现代化密码管理器，提供安全、可靠、易用的密码管理解决方案。

## ✨ 核心特性

### 🔐 密码管理
- ✅ 密码安全存储与管理
- ✅ 密码分类与标签
- ✅ 密码强度检测
- ✅ 智能密码生成器
- ✅ 密码搜索与筛选

### 📝 安全笔记
- ✅ 12种笔记类型（银行卡、身份证件、软件许可证等）
- ✅ 收藏和置顶功能
- ✅ 标签系统管理
- ✅ 敏感信息检测
- ✅ 内容预览功能

### 🛡️ TOTP二次验证
- ✅ 标准RFC 6238兼容
- ✅ 二维码扫描导入
- ✅ 多算法支持(SHA1/SHA256/SHA512)
- ✅ 实时验证码生成
- ✅ 倒计时显示

### 🔒 安全架构
- ✅ 零知识架构设计
- ✅ AES-256-GCM数据加密
- ✅ Argon2id主密码哈希
- ✅ 端到端加密保护
- ✅ 完整操作审计

## 🏗️ 技术架构

### 技术栈
- **框架**: Spring Boot 3.2.1 + JDK 17
- **认证**: SA-Token 1.38.0 (Spring Boot 3 兼容版)
- **数据库**: SQLite + MyBatis-Plus 3.5.5
- **加密**: BouncyCastle + Google Authenticator
- **架构**: 领域驱动设计(DDD)

### 项目结构
```
securefox/
├── src/main/java/club/gclmit/securefox/
│   ├── domain/                    # 领域层
│   │   ├── user/                 # 用户聚合
│   │   ├── vault/                # 密码库聚合
│   │   ├── note/                 # 安全笔记聚合
│   │   └── security/             # 安全领域服务
│   ├── application/              # 应用服务层
│   ├── infrastructure/           # 基础设施层
│   └── interfaces/               # 接口层
├── src/main/resources/
│   ├── application.yml           # 配置文件
│   └── db/                      # 数据库脚本
└── pom.xml                      # Maven依赖
```

## 🚀 快速开始

### 环境要求
- Java 21+
- Maven 3.6+

### 运行步骤
1. **克隆项目**
   ```bash
   git clone https://github.com/gclmit/securefox.git
   cd securefox
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **运行应用**
   ```bash
   mvn spring-boot:run
   ```

4. **访问应用**
   ```
   http://localhost:8080
   ```

### 数据库初始化
应用启动时会自动创建SQLite数据库文件和表结构，无需手动配置。

## 📚 API文档

### 用户管理
- `POST /api/v1/users/register` - 用户注册
- `POST /api/v1/users/login` - 用户登录
- `GET /api/v1/users/profile` - 获取用户信息

### 密码管理
- `POST /api/v1/passwords` - 创建密码
- `GET /api/v1/passwords` - 获取密码列表
- `PUT /api/v1/passwords/{id}` - 更新密码
- `DELETE /api/v1/passwords/{id}` - 删除密码

### TOTP管理
- `GET /api/v1/passwords/{id}/totp` - 获取TOTP验证码
- `POST /api/v1/passwords/{id}/totp` - 设置TOTP配置
- `DELETE /api/v1/passwords/{id}/totp` - 删除TOTP配置

### 密钥管理
- `POST /api/v1/keys` - 创建密钥
- `GET /api/v1/keys` - 获取密钥列表
- `PUT /api/v1/keys/{id}` - 更新密钥
- `DELETE /api/v1/keys/{id}` - 删除密钥

## 🔧 配置说明

### 安全配置
```yaml
securefox:
  security:
    master-password-min-length: 8
    session-timeout: 30
    max-login-attempts: 5
```

### 加密配置
```yaml
securefox:
  encryption:
    aes-key-length: 256
    argon2:
      memory: 65536
      iterations: 3
      parallelism: 4
```

## 🛡️ 安全特性

### 加密算法
- **主密码**: Argon2id (内存: 64MB, 迭代: 3次, 并行度: 4)
- **数据加密**: AES-256-GCM
- **密钥派生**: PBKDF2-SHA256 (迭代: 100,000次)

### 零知识架构
- 所有敏感数据在客户端加密
- 服务端无法解密用户数据
- 主密码不以明文形式传输或存储

## 📊 开发进度

- [x] **Phase 1**: 基础框架搭建
- [ ] **Phase 2**: 数据库设计
- [ ] **Phase 3**: 领域层核心
- [ ] **Phase 4**: 安全组件
- [ ] **Phase 5**: 基础设施层
- [ ] **Phase 6**: 应用服务层
- [ ] **Phase 7**: 接口层
- [ ] **Phase 8**: 密钥管理功能

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进SecureFox！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👨‍💻 作者

- **Claude 4.0 sonnet** - 初始开发

---

**SecureFox** - 让您的数字身份更安全 🦊🔒
