# SecureFox 智能密码管理器 - 技术方案

## 📋 项目概述

**项目名称**: SecureFox (安全狐)  
**项目描述**: 基于领域驱动设计(DDD)架构的现代化智能密码管理器  
**技术栈**: Spring Boot 3.2.1 + JDK 17 + SQLite + SA-Token + MyBatis-Plus  
**架构模式**: 经典分层DDD架构  
**包名**: `club.gclmit.securefox`

---

## 🏗️ 系统架构设计

### DDD分层架构实现

```
src/main/java/club/gclmit/securefox/
├── SecureFoxApplication.java         # 启动类
├── domain/                          # 领域层
│   ├── user/                       # 用户聚合
│   │   ├── User.java               # 聚合根
│   │   ├── UserRepository.java     # 仓储接口
│   │   └── UserDomainService.java  # 领域服务
│   ├── vault/                      # 密码库聚合
│   │   ├── Password.java           # 聚合根
│   │   ├── PasswordCategory.java   # 分类实体
│   │   ├── PasswordRepository.java # 仓储接口
│   │   └── VaultDomainService.java # 领域服务
│   ├── note/                       # 安全笔记聚合
│   │   ├── SecureNote.java         # 聚合根
│   │   ├── NoteType.java           # 笔记类型枚举
│   │   ├── SecureNoteRepository.java
│   │   └── SecureNoteDomainService.java
│   ├── audit/                      # 审计聚合
│   │   ├── AuditLog.java           # 审计日志实体
│   │   ├── AuditLogRepository.java
│   │   └── AuditService.java       # 审计服务接口
│   └── security/                   # 安全领域服务
│       ├── EncryptionService.java  # 加密服务接口
│       └── TotpService.java        # TOTP服务接口
├── application/                    # 应用服务层
│   ├── service/                    # 应用服务
│   ├── command/                    # 命令对象
│   └── dto/                        # 数据传输对象
├── infrastructure/                 # 基础设施层
│   ├── repository/                 # 仓储实现
│   ├── security/                   # 安全实现
│   ├── audit/                      # 审计实现
│   └── config/                     # 配置类
└── interfaces/                     # 接口层
    ├── controller/                 # REST控制器
    ├── dto/                        # 接口DTO
    └── exception/                  # 异常处理
```

### 核心聚合设计

#### 1. 用户聚合 (User Aggregate)
- **聚合根**: User
- **值对象**: Email, MasterPasswordHash
- **仓储**: UserRepository
- **领域服务**: UserDomainService

#### 2. 密码库聚合 (Vault Aggregate)
- **聚合根**: Password
- **实体**: PasswordCategory
- **值对象**: TotpConfig, EncryptedData
- **仓储**: PasswordRepository
- **领域服务**: VaultDomainService, TotpService

#### 3. 安全笔记聚合 (SecureNote Aggregate)
- **聚合根**: SecureNote
- **值对象**: NoteType, EncryptedContent
- **仓储**: SecureNoteRepository
- **领域服务**: SecureNoteDomainService

#### 4. 审计聚合 (Audit Aggregate)
- **聚合根**: AuditLog
- **仓储**: AuditLogRepository
- **领域服务**: AuditService

---

## 🔐 安全架构实现

### 加密算法配置
- **主密码哈希**: Argon2id (memory=65536, iterations=3, parallelism=4)
- **数据加密**: AES-256-GCM
- **密钥派生**: PBKDF2-SHA256 (iterations=100,000)
- **TOTP算法**: SHA1/SHA256/SHA512支持

### 零知识架构实现
```
用户输入主密码 → Argon2id哈希 → 存储哈希值
用户敏感数据 → AES-256-GCM加密 → 存储密文
服务端永远无法获取用户明文数据
```

### SA-Token认证配置
```yaml
sa-token:
  token-name: satoken
  timeout: 2592000        # 30天
  activity-timeout: 1800  # 30分钟无操作超时
  is-concurrent: true     # 允许并发登录
  max-login-count: 5      # 最大登录设备数
```

---

## 💾 数据库设计实现

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    master_password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 密码表 (passwords) - 包含TOTP支持
```sql
CREATE TABLE passwords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    encrypted_password TEXT NOT NULL,
    website VARCHAR(500),
    notes TEXT,
    category_id INTEGER,
    -- TOTP相关字段
    totp_secret TEXT,                    -- 加密的TOTP密钥
    totp_issuer VARCHAR(255),            -- TOTP发行者
    totp_account_name VARCHAR(255),      -- TOTP账户名
    totp_digits INTEGER DEFAULT 6,      -- 验证码位数
    totp_period INTEGER DEFAULT 30,     -- 时间间隔(秒)
    totp_algorithm VARCHAR(10) DEFAULT 'SHA1', -- 算法
    -- 同步相关字段
    sync_version INTEGER DEFAULT 1,
    last_modified_device VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES password_categories(id)
);
```

#### 安全笔记表 (secure_notes)
```sql
CREATE TABLE secure_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(200) NOT NULL,
    encrypted_content TEXT NOT NULL,
    note_type VARCHAR(50) NOT NULL,
    category_id INTEGER,
    tags VARCHAR(500),
    favorite TINYINT DEFAULT 0,
    pinned TINYINT DEFAULT 0,
    content_preview VARCHAR(200),
    sync_version INTEGER DEFAULT 1,
    last_modified_device VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id INTEGER,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

---

## 🛠️ 技术栈配置

### Maven依赖 (pom.xml)
```xml
<dependencies>
    <!-- Spring Boot 3.2.1 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- SA-Token 1.38.0 (Spring Boot 3兼容版) -->
    <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot3-starter</artifactId>
        <version>1.38.0</version>
    </dependency>
    
    <!-- MyBatis-Plus 3.5.5 -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.5</version>
    </dependency>
    
    <!-- SQLite JDBC -->
    <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
    </dependency>
    
    <!-- BouncyCastle加密库 -->
    <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk18on</artifactId>
        <version>1.77</version>
    </dependency>
    
    <!-- Google Authenticator (TOTP) -->
    <dependency>
        <groupId>com.warrenstrange</groupId>
        <artifactId>googleauth</artifactId>
        <version>1.5.0</version>
    </dependency>
    
    <!-- Liquibase数据库迁移 -->
    <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
    </dependency>
</dependencies>
```

### 应用配置 (application.yml)
```yaml
spring:
  application:
    name: securefox
  
  # SQLite数据源配置
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: *****************************
    username:
    password:

  # Liquibase配置
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true

# SA-Token配置
sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: 1800
  is-concurrent: true
  max-login-count: 5

# MyBatis-Plus配置
mybatis-plus:
  type-enums-package: club.gclmit.securefox.domain.*.enums
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto

# SecureFox自定义配置
securefox:
  security:
    master-password-min-length: 8
    session-timeout: 30
    max-login-attempts: 5
  encryption:
    aes-key-length: 256
    argon2:
      memory: 65536
      iterations: 3
      parallelism: 4
  totp:
    default-digits: 6
    default-period: 30
    default-algorithm: SHA1
    time-tolerance: 1
```

---

## 🎨 API接口设计

### 统一响应格式 (ApiResponse)
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...},
  "timestamp": 1706677200000
}
```

### 1. 用户管理接口 (`/api/v1/users`)

#### 用户注册
```http
POST /api/v1/users/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}
```

#### 用户登录
```http
POST /api/v1/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}
```

#### 获取用户信息
```http
GET /api/v1/users/profile
Authorization: satoken {token}
```

### 2. 密码管理接口 (`/api/v1/passwords`)

#### 创建密码
```http
POST /api/v1/passwords
Authorization: satoken {token}
Content-Type: application/json

{
  "title": "GitHub",
  "username": "myusername",
  "password": "MySecurePassword123!",
  "website": "https://github.com",
  "categoryId": 1,
  "notes": "Work account"
}
```

#### 获取密码列表
```http
GET /api/v1/passwords
Authorization: satoken {token}
```

#### 获取密码明文
```http
GET /api/v1/passwords/{id}/plaintext
Authorization: satoken {token}
```

#### 搜索密码
```http
GET /api/v1/passwords/search?keyword=github
Authorization: satoken {token}
```

### 3. TOTP管理接口

#### 生成TOTP验证码
```http
GET /api/v1/passwords/{id}/totp/code
Authorization: satoken {token}
```

#### 设置TOTP配置
```http
POST /api/v1/passwords/{id}/totp
Authorization: satoken {token}
Content-Type: application/json

{
  "secret": "JBSWY3DPEHPK3PXP",
  "issuer": "GitHub",
  "accountName": "myusername",
  "digits": 6,
  "period": 30,
  "algorithm": "SHA1"
}
```

### 4. 安全笔记接口 (`/api/v1/notes`)

#### 创建安全笔记
```http
POST /api/v1/notes
Authorization: satoken {token}
Content-Type: application/json

{
  "title": "银行卡信息",
  "content": "卡号：1234 5678 9012 3456\nCVV：123\n有效期：12/25",
  "noteType": "BANK_CARD",
  "tags": "个人,银行",
  "favorite": false,
  "pinned": false
}
```

#### 获取笔记类型列表
```http
GET /api/v1/notes/types
Authorization: satoken {token}
```

### 5. 导入导出接口 (`/api/v1/import-export`)

#### 导入Bitwarden数据
```http
POST /api/v1/import-export/bitwarden/import
Authorization: satoken {token}
Content-Type: multipart/form-data

file: bitwarden_export.json
```

#### 导出为Bitwarden格式
```http
GET /api/v1/import-export/bitwarden/export
Authorization: satoken {token}
```

---

## 🔧 核心实现特性

### 领域驱动设计实现
- **聚合根**: User、Password、SecureNote独立管理
- **值对象**: TotpConfig、NoteType等封装业务概念
- **领域服务**: 跨聚合的业务逻辑处理
- **仓储模式**: 统一的数据访问抽象

### 安全实现特性
- **零知识架构**: 服务端无法获取用户明文数据
- **端到端加密**: AES-256-GCM加密所有敏感数据
- **主密码保护**: Argon2id哈希算法防暴力破解
- **会话管理**: SA-Token提供安全的会话控制
- **操作审计**: 完整记录所有敏感操作

### TOTP二次验证特性
- **标准兼容**: 完全兼容RFC 6238标准
- **多算法支持**: SHA1、SHA256、SHA512
- **实时生成**: 30秒刷新的6位验证码
- **二维码支持**: 扫描导入TOTP配置
- **时间容差**: 支持前后时间窗口验证

### 数据管理特性
- **Bitwarden兼容**: 支持主流密码管理器数据迁移
- **逻辑删除**: 数据安全删除，支持恢复
- **版本控制**: 数据同步版本管理
- **分类管理**: 灵活的密码和笔记分类系统

---

## 📊 项目实施状态

### ✅ 已完成功能
- [x] Spring Boot 3.2.1 + JDK 17 基础框架
- [x] DDD分层架构完整实现
- [x] SQLite + MyBatis-Plus 数据访问层
- [x] SA-Token 认证授权系统
- [x] 用户注册登录完整流程
- [x] 密码CRUD和加密存储
- [x] TOTP二次验证完整支持
- [x] 安全笔记12种类型管理
- [x] Bitwarden格式导入导出
- [x] 操作审计日志系统
- [x] 统一异常处理和响应格式
- [x] Liquibase数据库版本管理

### 🎯 技术亮点
1. **现代化技术栈**: Spring Boot 3 + JDK 17
2. **企业级架构**: DDD + 分层架构设计
3. **安全第一**: 零知识 + 端到端加密
4. **标准兼容**: RFC 6238 TOTP + Bitwarden格式
5. **开发友好**: 完整的API文档和测试支持

---

## 📝 总结

SecureFox 是一个基于现代化技术栈和DDD架构的智能密码管理器，具备以下核心优势：

### 🔒 安全性
- 采用Argon2id + AES-256-GCM的军用级加密标准
- 零知识架构确保服务端无法获取用户明文数据
- 完整的操作审计和安全监控体系

### 🏗️ 架构性
- 领域驱动设计确保代码质量和可维护性
- 分层架构支持功能扩展和技术升级
- 统一的API设计和响应格式

### 🚀 功能性
- 完整的密码管理和TOTP二次验证
- 12种安全笔记类型支持
- Bitwarden格式数据迁移兼容
- 灵活的分类和标签管理系统

### 💡 创新性
- 基于Spring Boot 3的现代化实现
- 轻量级SQLite数据库适合个人使用
- 完整的HTTP API支持多端开发

---

**文档版本**: v1.0
**最后更新**: 2025-01-31
**作者**: Claude 4.0 sonnet
**项目状态**: 生产就绪
