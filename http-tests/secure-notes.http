### SecureFox 安全笔记管理 API 测试
### 
### 前置条件：
### 1. 请先运行 user-management.http 中的用户注册和登录
### 2. 确保已获得有效的token
### 
### 测试覆盖：
### - 12种笔记类型管理
### - 笔记CRUD操作
### - 收藏和置顶功能
### - 标签系统
### - 内容安全检测
###

### 1. 获取笔记类型列表
GET {{baseUrl}}/api/v1/notes/types
Authorization: satoken {{token}}

> {%
client.test("获取笔记类型列表成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    client.assert(response.body.data.length >= 10, "应有多种笔记类型");
    
    console.log("笔记类型列表获取成功，共 " + response.body.data.length + " 种类型");
    
    // 查找银行卡类型
    const bankCardType = response.body.data.find(type => type.name === "BANK_CARD");
    if (bankCardType) {
        console.log("银行卡类型: " + bankCardType.description);
    }
});
%}

###

### 2. 创建安全笔记 - 银行卡信息
POST {{baseUrl}}/api/v1/notes
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "招商银行信用卡",
  "content": "卡号：1234 5678 9012 3456\nCVV：123\n有效期：12/25\n持卡人：张三\n银行：招商银行\n卡类型：信用卡",
  "noteType": "BANK_CARD",
  "tags": "个人,银行,信用卡",
  "favorite": true,
  "pinned": false
}

> {%
client.test("创建银行卡笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.message === "安全笔记创建成功", "消息应正确");
    client.assert(response.body.data.title === "招商银行信用卡", "标题应正确");
    client.assert(response.body.data.noteType === "BANK_CARD", "笔记类型应正确");
    client.assert(response.body.data.favorite === true, "收藏状态应正确");
    
    // 保存笔记ID用于后续测试
    client.global.set("noteId", response.body.data.id);
    console.log("银行卡笔记创建成功，ID: " + response.body.data.id);
});
%}

###

### 3. 创建安全笔记 - 身份证件
POST {{baseUrl}}/api/v1/notes
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "身份证信息",
  "content": "姓名：张三\n身份证号：110101199001011234\n签发机关：北京市公安局\n有效期限：2020.01.01-2030.01.01\n地址：北京市朝阳区xxx街道",
  "noteType": "IDENTITY_CARD",
  "tags": "个人,证件,身份证",
  "favorite": false,
  "pinned": true
}

> {%
client.test("创建身份证笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.noteType === "IDENTITY_CARD", "笔记类型应正确");
    client.assert(response.body.data.pinned === true, "置顶状态应正确");
    
    // 保存第二个笔记ID
    client.global.set("noteId2", response.body.data.id);
    console.log("身份证笔记创建成功，ID: " + response.body.data.id);
});
%}

###

### 4. 创建安全笔记 - 软件许可证
POST {{baseUrl}}/api/v1/notes
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "IntelliJ IDEA 许可证",
  "content": "产品：IntelliJ IDEA Ultimate\n许可证密钥：XXXX-XXXX-XXXX-XXXX\n到期时间：2025-12-31\n授权用户：开发团队\n购买日期：2024-01-01",
  "noteType": "SOFTWARE_LICENSE",
  "tags": "工作,软件,开发工具",
  "favorite": false,
  "pinned": false
}

> {%
client.test("创建软件许可证笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.noteType === "SOFTWARE_LICENSE", "笔记类型应正确");
    
    console.log("软件许可证笔记创建成功");
});
%}

###

### 5. 获取笔记列表 - 全部笔记
GET {{baseUrl}}/api/v1/notes
Authorization: satoken {{token}}

> {%
client.test("获取笔记列表成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    client.assert(response.body.data.length >= 3, "应至少有3个笔记");
    
    console.log("笔记列表获取成功，共 " + response.body.data.length + " 个笔记");
});
%}

###

### 6. 获取笔记列表 - 按类型筛选
GET {{baseUrl}}/api/v1/notes?type=BANK_CARD
Authorization: satoken {{token}}

> {%
client.test("按类型筛选笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    
    // 验证所有返回的笔记都是银行卡类型
    response.body.data.forEach(note => {
        client.assert(note.noteType === "BANK_CARD", "所有笔记都应为银行卡类型");
    });
    
    console.log("银行卡类型笔记筛选成功，共 " + response.body.data.length + " 个");
});
%}

###

### 7. 获取笔记列表 - 收藏笔记
GET {{baseUrl}}/api/v1/notes?favorite=true
Authorization: satoken {{token}}

> {%
client.test("获取收藏笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    
    // 验证所有返回的笔记都是收藏状态
    response.body.data.forEach(note => {
        client.assert(note.favorite === true, "所有笔记都应为收藏状态");
    });
    
    console.log("收藏笔记获取成功，共 " + response.body.data.length + " 个");
});
%}

###

### 8. 获取笔记详情
GET {{baseUrl}}/api/v1/notes/{{noteId}}
Authorization: satoken {{token}}

> {%
client.test("获取笔记详情成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.title === "招商银行信用卡", "标题应正确");
    client.assert(response.body.data.id == client.global.get("noteId"), "ID应匹配");
    
    console.log("笔记详情获取成功: " + response.body.data.title);
});
%}

###

### 9. 获取笔记完整内容
GET {{baseUrl}}/api/v1/notes/{{noteId}}/content
Authorization: satoken {{token}}

> {%
client.test("获取笔记内容成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.includes("1234 5678 9012 3456"), "内容应包含卡号");
    client.assert(response.body.data.includes("招商银行"), "内容应包含银行名称");
    
    console.log("笔记内容获取成功");
});
%}

###

### 10. 更新笔记
PUT {{baseUrl}}/api/v1/notes/{{noteId}}
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "招商银行信用卡（已更新）",
  "content": "卡号：1234 5678 9012 3456\nCVV：123\n有效期：12/25\n持卡人：张三\n银行：招商银行\n卡类型：信用卡\n备注：主要用于日常消费",
  "noteType": "BANK_CARD",
  "tags": "个人,银行,信用卡,日常",
  "favorite": true,
  "pinned": true
}

> {%
client.test("更新笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.message === "安全笔记更新成功", "消息应正确");
    client.assert(response.body.data.title === "招商银行信用卡（已更新）", "标题应已更新");
    client.assert(response.body.data.pinned === true, "置顶状态应已更新");
    
    console.log("笔记更新成功");
});
%}

###

### 11. 搜索笔记
GET {{baseUrl}}/api/v1/notes/search?keyword=银行
Authorization: satoken {{token}}

> {%
client.test("搜索笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    client.assert(response.body.data.length >= 1, "应找到至少1个结果");
    
    console.log("笔记搜索成功，找到 " + response.body.data.length + " 个结果");
});
%}

###

### 12. 内容安全检测
POST {{baseUrl}}/api/v1/notes/security-check
Authorization: satoken {{token}}
Content-Type: application/json

{
  "content": "这是一个包含身份证号110101199001011234和银行卡号1234567890123456的测试内容"
}

> {%
client.test("内容安全检测成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.hasSensitiveInfo === true, "应检测到敏感信息");
    client.assert(Array.isArray(response.body.data.detectedTypes), "检测类型应为数组");
    
    console.log("内容安全检测完成，检测到敏感信息类型: " + response.body.data.detectedTypes.join(", "));
});
%}

###

### 13. 删除笔记
DELETE {{baseUrl}}/api/v1/notes/{{noteId2}}
Authorization: satoken {{token}}

> {%
client.test("删除笔记成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data === "安全笔记删除成功", "消息应正确");
    
    console.log("笔记删除成功");
});
%}

###

### 测试完成提示
### 
### 安全笔记管理API测试用例说明：
### 1. 获取笔记类型列表 - 查看支持的12种笔记类型
### 2-4. 创建不同类型笔记 - 银行卡、身份证、软件许可证
### 5-7. 笔记列表查询 - 全部、按类型、按收藏状态
### 8-9. 笔记详情和内容获取 - 验证数据完整性
### 10. 更新笔记 - 测试修改功能
### 11. 搜索笔记 - 验证关键词搜索
### 12. 内容安全检测 - 验证敏感信息识别
### 13. 删除笔记 - 测试删除功能
### 
### 支持的笔记类型：
### - BANK_CARD: 银行卡
### - IDENTITY_CARD: 身份证件
### - SOFTWARE_LICENSE: 软件许可证
### - SERVER_INFO: 服务器信息
### - DATABASE_CONFIG: 数据库配置
### - API_CREDENTIALS: API凭据
### - PERSONAL_INFO: 个人信息
### - MEDICAL_INFO: 医疗信息
### - INSURANCE_INFO: 保险信息
### - TRAVEL_INFO: 旅行信息
### - EDUCATION_INFO: 教育信息
### - OTHER: 其他类型
