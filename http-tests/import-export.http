### SecureFox 数据导入导出 API 测试
### 
### 前置条件：
### 1. 请先运行 user-management.http 中的用户注册和登录
### 2. 确保已获得有效的token
### 3. 准备测试用的Bitwarden导出文件
### 
### 测试覆盖：
### - Bitwarden格式数据导入
### - 数据导出功能
### - 文件格式验证
### - 错误处理测试
###

### 1. 导出当前数据为Bitwarden格式
GET {{baseUrl}}/api/v1/import-export/bitwarden/export
Authorization: satoken {{token}}

> {%
client.test("导出数据成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    
    // 检查响应头
    const contentType = response.headers.valueOf("Content-Type");
    const contentDisposition = response.headers.valueOf("Content-Disposition");
    
    client.assert(contentType.includes("application/json"), "内容类型应为JSON");
    client.assert(contentDisposition.includes("attachment"), "应为附件下载");
    client.assert(contentDisposition.includes("securefox_export_"), "文件名应包含导出标识");
    
    // 检查导出数据结构
    if (typeof response.body === 'object') {
        client.assert(response.body.encrypted === false, "导出数据应为未加密");
        client.assert(Array.isArray(response.body.folders), "应包含文件夹数组");
        client.assert(Array.isArray(response.body.items), "应包含项目数组");
        
        console.log("数据导出成功，包含 " + response.body.items.length + " 个项目");
    }
});
%}

###

### 2. 创建测试用的Bitwarden导入数据
### 注意：这是一个模拟的导入测试，实际使用时需要上传真实的JSON文件

### 准备测试数据（保存为bitwarden_test.json）
### {
###   "encrypted": false,
###   "folders": [
###     {
###       "id": "folder1",
###       "name": "Work"
###     }
###   ],
###   "items": [
###     {
###       "id": "item1",
###       "organizationId": null,
###       "folderId": "folder1",
###       "type": 1,
###       "name": "Test Website",
###       "notes": "Test notes",
###       "favorite": false,
###       "login": {
###         "username": "testuser",
###         "password": "testpassword123",
###         "totp": null,
###         "uris": [
###           {
###             "match": null,
###             "uri": "https://test.example.com"
###           }
###         ]
###       },
###       "creationDate": "2024-01-30T10:00:00.000Z",
###       "revisionDate": "2024-01-30T10:00:00.000Z"
###     }
###   ]
### }

### 3. 导入Bitwarden数据（需要实际文件）
### 注意：此测试需要实际的文件上传，在HTTP Client中需要手动选择文件
POST {{baseUrl}}/api/v1/import-export/bitwarden/import
Authorization: satoken {{token}}
Content-Type: multipart/form-data

# 在实际测试中，需要在这里上传bitwarden_test.json文件
# file=@/path/to/bitwarden_test.json

> {%
client.test("导入数据测试", function() {
    // 由于无法在HTTP文件中直接上传文件，这里只做基本的响应检查
    if (response.status === 200) {
        client.assert(response.body.code === 200, "响应码应为200");
        client.assert(response.body.data.totalItems !== undefined, "应返回总项目数");
        client.assert(response.body.data.importedPasswords !== undefined, "应返回导入密码数");
        
        console.log("数据导入成功:");
        console.log("- 总项目数: " + response.body.data.totalItems);
        console.log("- 导入密码数: " + response.body.data.importedPasswords);
        console.log("- 导入笔记数: " + response.body.data.importedNotes);
        console.log("- 跳过项目数: " + response.body.data.skippedItems);
    } else if (response.status === 400) {
        console.log("导入测试：需要上传实际的JSON文件");
        client.assert(response.body.message.includes("请选择要导入的文件"), "应提示选择文件");
    }
});
%}

###

### 4. 测试导入错误 - 空文件
POST {{baseUrl}}/api/v1/import-export/bitwarden/import
Authorization: satoken {{token}}
Content-Type: multipart/form-data

# 不上传任何文件

> {%
client.test("空文件导入失败", function() {
    client.assert(response.status === 400, "HTTP状态码应为400");
    client.assert(response.body.code !== 200, "响应码不应为200");
    client.assert(response.body.message.includes("请选择要导入的文件"), "应提示选择文件");
    
    console.log("空文件导入测试通过");
});
%}

###

### 5. 测试导入错误 - 非JSON文件（模拟）
### 注意：实际测试时需要上传一个非JSON格式的文件
POST {{baseUrl}}/api/v1/import-export/bitwarden/import
Authorization: satoken {{token}}
Content-Type: multipart/form-data

# 在实际测试中，上传一个.txt文件来测试格式验证
# file=@/path/to/test.txt

> {%
client.test("非JSON文件导入失败", function() {
    if (response.status === 400) {
        client.assert(response.body.code !== 200, "响应码不应为200");
        client.assert(response.body.message.includes("只支持JSON格式"), "应提示格式错误");
        
        console.log("非JSON文件导入测试通过");
    } else {
        console.log("非JSON文件导入测试：需要上传实际的非JSON文件");
    }
});
%}

###

### 6. 获取导入导出帮助信息
### 注意：这个接口可能需要根据实际实现调整
GET {{baseUrl}}/api/v1/import-export/help
Authorization: satoken {{token}}

> {%
client.test("获取帮助信息", function() {
    if (response.status === 200) {
        client.assert(response.body.code === 200, "响应码应为200");
        
        console.log("帮助信息获取成功");
    } else if (response.status === 404) {
        console.log("帮助信息接口未实现，这是正常的");
    }
});
%}

###

### 7. 测试大文件导入限制（模拟）
### 注意：实际测试时需要上传一个超过10MB的文件
POST {{baseUrl}}/api/v1/import-export/bitwarden/import
Authorization: satoken {{token}}
Content-Type: multipart/form-data

# 在实际测试中，上传一个大于10MB的文件来测试大小限制
# file=@/path/to/large_file.json

> {%
client.test("大文件导入限制测试", function() {
    if (response.status === 400) {
        client.assert(response.body.code !== 200, "响应码不应为200");
        client.assert(response.body.message.includes("文件大小不能超过"), "应提示文件大小限制");
        
        console.log("大文件导入限制测试通过");
    } else {
        console.log("大文件导入测试：需要上传实际的大文件");
    }
});
%}

###

### 8. 验证导入后的数据
### 导入成功后，验证数据是否正确导入
GET {{baseUrl}}/api/v1/passwords
Authorization: satoken {{token}}

> {%
client.test("验证导入后的密码数据", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    
    // 查找可能导入的测试数据
    const testItem = response.body.data.find(item => item.title === "Test Website");
    if (testItem) {
        console.log("找到导入的测试数据: " + testItem.title);
        client.assert(testItem.username === "testuser", "导入的用户名应正确");
        client.assert(testItem.website === "https://test.example.com", "导入的网站应正确");
    }
    
    console.log("导入后数据验证完成，当前密码总数: " + response.body.data.length);
});
%}

###

### 9. 验证导入后的笔记数据
GET {{baseUrl}}/api/v1/notes
Authorization: satoken {{token}}

> {%
client.test("验证导入后的笔记数据", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    
    console.log("导入后笔记验证完成，当前笔记总数: " + response.body.data.length);
});
%}

###

### 测试完成提示
### 
### 数据导入导出API测试用例说明：
### 1. 导出当前数据 - 验证Bitwarden格式导出
### 2-3. 数据导入测试 - 需要实际文件上传
### 4-5. 错误处理测试 - 空文件、非JSON文件
### 6. 帮助信息获取 - 可选功能
### 7. 大文件限制测试 - 验证文件大小限制
### 8-9. 导入后数据验证 - 确认数据正确导入
### 
### 实际测试步骤：
### 1. 先运行导出接口，保存导出的JSON文件
### 2. 创建测试用的Bitwarden格式JSON文件
### 3. 在HTTP Client中手动上传文件进行导入测试
### 4. 验证导入后的数据完整性
### 
### 支持的导入格式：
### - Bitwarden JSON格式（未加密）
### - 文件大小限制：10MB
### - 支持的数据类型：登录项、安全笔记、文件夹
### 
### 注意事项：
### - 导入会自动去重，避免重复数据
### - 导入过程中的错误会在响应中详细说明
### - 建议在导入前备份现有数据
### - 导出的文件包含明文密码，请妥善保管
