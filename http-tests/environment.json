{"development": {"baseUrl": "http://localhost:8080", "timeout": 30000, "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "variables": {"token": "", "userId": "", "passwordId": "", "noteId": "", "categoryId": ""}}, "production": {"baseUrl": "https://securefox.example.com", "timeout": 30000, "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "variables": {"token": "", "userId": "", "passwordId": "", "noteId": "", "categoryId": ""}}, "test": {"baseUrl": "http://localhost:8080", "timeout": 10000, "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "variables": {"token": "", "userId": "", "passwordId": "", "noteId": "", "categoryId": ""}}}