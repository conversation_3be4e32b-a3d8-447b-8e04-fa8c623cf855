### SecureFox 用户管理 API 测试
### 
### 测试环境配置：
### - 开发环境: http://localhost:8080
### - 生产环境: https://securefox.example.com
### 
### 使用说明：
### 1. 按顺序执行测试用例
### 2. 登录成功后会获得token，用于后续API调用
### 3. 修改测试数据中的邮箱和用户名避免冲突
###

### 1. 用户注册
POST {{baseUrl}}/api/v1/users/register
Content-Type: application/json

{
  "username": "testuser001",
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}

> {%
client.test("用户注册成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.message === "注册成功", "消息应为注册成功");
    client.assert(response.body.data.username === "testuser001", "用户名应正确");
    client.assert(response.body.data.email === "<EMAIL>", "邮箱应正确");
    
    // 保存用户ID用于后续测试
    client.global.set("userId", response.body.data.id);
    console.log("用户注册成功，用户ID: " + response.body.data.id);
});
%}

###

### 2. 用户登录
POST {{baseUrl}}/api/v1/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}

> {%
client.test("用户登录成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.message === "登录成功", "消息应为登录成功");
    client.assert(response.body.data.token !== undefined, "应返回token");
    client.assert(response.body.data.user.email === "<EMAIL>", "用户邮箱应正确");
    
    // 保存token用于后续API调用
    client.global.set("token", response.body.data.token);
    console.log("用户登录成功，Token: " + response.body.data.token.substring(0, 20) + "...");
});
%}

###

### 3. 获取当前用户信息
GET {{baseUrl}}/api/v1/users/profile
Authorization: satoken {{token}}

> {%
client.test("获取用户信息成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.username === "testuser001", "用户名应正确");
    client.assert(response.body.data.email === "<EMAIL>", "邮箱应正确");
    client.assert(response.body.data.id !== undefined, "应有用户ID");
    
    console.log("用户信息获取成功: " + response.body.data.username);
});
%}

###

### 4. 用户登出
POST {{baseUrl}}/api/v1/users/logout
Authorization: satoken {{token}}

> {%
client.test("用户登出成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data === "登出成功", "应返回登出成功消息");
    
    console.log("用户登出成功");
});
%}

###

### 5. 登录错误测试 - 错误的邮箱
POST {{baseUrl}}/api/v1/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}

> {%
client.test("错误邮箱登录失败", function() {
    client.assert(response.status === 400 || response.status === 401, "HTTP状态码应为400或401");
    client.assert(response.body.code !== 200, "响应码不应为200");
    
    console.log("错误邮箱登录测试通过");
});
%}

###

### 6. 登录错误测试 - 错误的密码
POST {{baseUrl}}/api/v1/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "masterPassword": "WrongPassword123!"
}

> {%
client.test("错误密码登录失败", function() {
    client.assert(response.status === 400 || response.status === 401, "HTTP状态码应为400或401");
    client.assert(response.body.code !== 200, "响应码不应为200");
    
    console.log("错误密码登录测试通过");
});
%}

###

### 7. 注册错误测试 - 重复邮箱
POST {{baseUrl}}/api/v1/users/register
Content-Type: application/json

{
  "username": "testuser002",
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}

> {%
client.test("重复邮箱注册失败", function() {
    client.assert(response.status === 400, "HTTP状态码应为400");
    client.assert(response.body.code !== 200, "响应码不应为200");
    
    console.log("重复邮箱注册测试通过");
});
%}

###

### 8. 注册错误测试 - 无效数据
POST {{baseUrl}}/api/v1/users/register
Content-Type: application/json

{
  "username": "ab",
  "email": "invalid-email",
  "masterPassword": "123"
}

> {%
client.test("无效数据注册失败", function() {
    client.assert(response.status === 400, "HTTP状态码应为400");
    client.assert(response.body.code !== 200, "响应码不应为200");
    
    console.log("无效数据注册测试通过");
});
%}

###

### 9. 未认证访问测试
GET {{baseUrl}}/api/v1/users/profile

> {%
client.test("未认证访问失败", function() {
    client.assert(response.status === 401, "HTTP状态码应为401");
    
    console.log("未认证访问测试通过");
});
%}

###

### 测试完成提示
### 
### 用户管理API测试用例说明：
### 1. 用户注册 - 创建新用户账户
### 2. 用户登录 - 验证用户凭据并获取token
### 3. 获取用户信息 - 验证认证状态
### 4. 用户登出 - 清除会话
### 5-9. 错误场景测试 - 验证异常处理
### 
### 注意事项：
### - 请确保SecureFox服务已启动
### - 首次运行前请修改测试邮箱避免冲突
### - token会自动保存到全局变量中
### - 所有测试用例都包含断言验证
