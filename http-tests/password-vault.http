### SecureFox 密码库管理 API 测试
### 
### 前置条件：
### 1. 请先运行 user-management.http 中的用户注册和登录
### 2. 确保已获得有效的token
### 
### 测试覆盖：
### - 密码CRUD操作
### - 密码搜索功能
### - TOTP二次验证
### - 密码强度检测
###

### 1. 创建密码 - GitHub账户
POST {{baseUrl}}/api/v1/passwords
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "GitHub",
  "username": "myusername",
  "password": "MySecurePassword123!",
  "website": "https://github.com",
  "categoryId": 1,
  "notes": "Work account for development"
}

> {%
client.test("创建密码成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.message === "密码创建成功", "消息应正确");
    client.assert(response.body.data.title === "GitHub", "标题应正确");
    client.assert(response.body.data.username === "myusername", "用户名应正确");
    client.assert(response.body.data.website === "https://github.com", "网站应正确");
    
    // 保存密码ID用于后续测试
    client.global.set("passwordId", response.body.data.id);
    console.log("密码创建成功，ID: " + response.body.data.id);
});
%}

###

### 2. 创建密码 - Google账户
POST {{baseUrl}}/api/v1/passwords
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "Google",
  "username": "<EMAIL>",
  "password": "AnotherSecurePassword456!",
  "website": "https://google.com",
  "categoryId": 1,
  "notes": "Personal Google account"
}

> {%
client.test("创建第二个密码成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.title === "Google", "标题应正确");
    
    // 保存第二个密码ID
    client.global.set("passwordId2", response.body.data.id);
    console.log("第二个密码创建成功，ID: " + response.body.data.id);
});
%}

###

### 3. 获取密码列表
GET {{baseUrl}}/api/v1/passwords
Authorization: satoken {{token}}

> {%
client.test("获取密码列表成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    client.assert(response.body.data.length >= 2, "应至少有2个密码");
    
    console.log("密码列表获取成功，共 " + response.body.data.length + " 个密码");
});
%}

###

### 4. 获取密码详情
GET {{baseUrl}}/api/v1/passwords/{{passwordId}}
Authorization: satoken {{token}}

> {%
client.test("获取密码详情成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.title === "GitHub", "标题应正确");
    client.assert(response.body.data.id == client.global.get("passwordId"), "ID应匹配");
    
    console.log("密码详情获取成功: " + response.body.data.title);
});
%}

###

### 5. 获取密码明文
GET {{baseUrl}}/api/v1/passwords/{{passwordId}}/plaintext
Authorization: satoken {{token}}

> {%
client.test("获取密码明文成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data === "MySecurePassword123!", "明文密码应正确");
    
    console.log("密码明文获取成功");
});
%}

###

### 6. 更新密码
PUT {{baseUrl}}/api/v1/passwords/{{passwordId}}
Authorization: satoken {{token}}
Content-Type: application/json

{
  "title": "GitHub Updated",
  "username": "myusername",
  "password": "UpdatedSecurePassword789!",
  "website": "https://github.com",
  "categoryId": 1,
  "notes": "Updated work account for development"
}

> {%
client.test("更新密码成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.message === "密码更新成功", "消息应正确");
    client.assert(response.body.data.title === "GitHub Updated", "标题应已更新");
    
    console.log("密码更新成功");
});
%}

###

### 7. 搜索密码 - 关键词搜索
GET {{baseUrl}}/api/v1/passwords/search?keyword=github
Authorization: satoken {{token}}

> {%
client.test("搜索密码成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(Array.isArray(response.body.data), "数据应为数组");
    client.assert(response.body.data.length >= 1, "应找到至少1个结果");
    
    console.log("密码搜索成功，找到 " + response.body.data.length + " 个结果");
});
%}

###

### 8. 检查密码强度 - 强密码
POST {{baseUrl}}/api/v1/passwords/strength
Authorization: satoken {{token}}
Content-Type: application/json

{
  "password": "MyVerySecurePassword123!@#"
}

> {%
client.test("强密码检测成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data >= 3, "强密码评分应较高");
    
    console.log("密码强度检测成功，评分: " + response.body.data);
});
%}

###

### 9. 检查密码强度 - 弱密码
POST {{baseUrl}}/api/v1/passwords/strength
Authorization: satoken {{token}}
Content-Type: application/json

{
  "password": "123456"
}

> {%
client.test("弱密码检测成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data <= 2, "弱密码评分应较低");
    
    console.log("弱密码强度检测成功，评分: " + response.body.data);
});
%}

###

### 10. 设置TOTP配置
POST {{baseUrl}}/api/v1/passwords/{{passwordId}}/totp
Authorization: satoken {{token}}
Content-Type: application/json

{
  "secret": "JBSWY3DPEHPK3PXP",
  "issuer": "GitHub",
  "accountName": "myusername",
  "digits": 6,
  "period": 30,
  "algorithm": "SHA1"
}

> {%
client.test("设置TOTP配置成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    
    console.log("TOTP配置设置成功");
});
%}

###

### 11. 生成TOTP验证码
GET {{baseUrl}}/api/v1/passwords/{{passwordId}}/totp/code
Authorization: satoken {{token}}

> {%
client.test("生成TOTP验证码成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data.code !== undefined, "应返回验证码");
    client.assert(response.body.data.remainingSeconds !== undefined, "应返回剩余时间");
    client.assert(response.body.data.issuer === "GitHub", "发行者应正确");
    
    console.log("TOTP验证码生成成功: " + response.body.data.code);
    console.log("剩余时间: " + response.body.data.remainingSeconds + " 秒");
});
%}

###

### 12. 删除密码
DELETE {{baseUrl}}/api/v1/passwords/{{passwordId2}}
Authorization: satoken {{token}}

> {%
client.test("删除密码成功", function() {
    client.assert(response.status === 200, "HTTP状态码应为200");
    client.assert(response.body.code === 200, "响应码应为200");
    client.assert(response.body.data === "密码删除成功", "消息应正确");
    
    console.log("密码删除成功");
});
%}

###

### 测试完成提示
### 
### 密码库管理API测试用例说明：
### 1-2. 创建密码 - 测试密码创建功能
### 3. 获取密码列表 - 验证列表查询
### 4. 获取密码详情 - 验证单个密码查询
### 5. 获取密码明文 - 验证解密功能
### 6. 更新密码 - 测试密码修改
### 7. 搜索密码 - 验证搜索功能
### 8-9. 密码强度检测 - 验证强弱密码评分
### 10-11. TOTP功能 - 验证二次验证
### 12. 删除密码 - 测试删除功能
### 
### 注意事项：
### - 需要先完成用户登录获取token
### - TOTP验证码每30秒刷新一次
### - 密码明文获取会记录审计日志
### - 删除操作为逻辑删除，可恢复
