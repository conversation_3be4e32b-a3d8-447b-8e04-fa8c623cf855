# SecureFox Git忽略文件

# ==================== Java相关 ====================
# 编译输出
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# ==================== Spring Boot相关 ====================
# Spring Boot DevTools
.spring-boot-devtools.properties

# Spring Boot 配置
application-local.yml
application-dev.yml
application-prod.yml

# ==================== 数据库相关 ====================
# SQLite数据库文件
data/
*.db
*.sqlite
*.sqlite3
*.db-journal
*.db-wal
*.db-shm

# H2数据库
*.h2.db
*.trace.db

# ==================== 日志文件 ====================
logs/
*.log
*.log.*
*.gz

# ==================== IDE相关 ====================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
.idea_modules/

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath

# VS Code
.vscode/
*.code-workspace

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ==================== 操作系统相关 ====================
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==================== 安全相关 ====================
# 敏感配置文件
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore
secrets/
.env
.env.local
.env.production

# 测试数据
test-data/
mock-data/
sample-data/

# ==================== 构建和部署相关 ====================
# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
k8s-local/
*.kubeconfig

# 临时文件
*.tmp
*.temp
*.cache
.cache/

# 备份文件
*.backup
*.bak
*.orig

# ==================== 开发工具相关 ====================
# JProfiler
*.jprofiler

# YourKit
*.yjp

# JRebel
rebel.xml

# SonarQube
.sonar/

# ==================== HTTP测试相关 ====================
# HTTP Client临时文件
http-client.private.env.json
.httpRequests/

# ==================== 项目特定 ====================
# SecureFox特定忽略
# 用户上传的导入文件
uploads/
import-files/
export-files/

# 临时生成的二维码
qrcodes/
temp-qr/

# 性能测试结果
performance-test-results/
load-test-results/

# 文档生成临时文件
docs-temp/
api-docs-temp/
