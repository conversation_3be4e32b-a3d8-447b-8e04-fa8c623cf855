# SecureFox 智能密码管理器 - 完整技术方案

## 📋 项目概述

**项目名称**: SecureFox (安全狐)  
**项目描述**: 基于领域驱动设计(DDD)架构的智能密码管理器  
**技术栈**: Spring Boot + SQLite + SA-Token + MyBatis-Plus  
**架构模式**: 经典分层DDD架构  
**包名**: `club.gclmit.securefox`

---

## 🎯 核心功能需求

### 1. 用户管理功能
- ✅ 用户注册与登录
- ✅ 主密码设置与验证
- ✅ 多设备登录管理
- ✅ 会话安全控制

### 2. 密码管理功能
- ✅ 密码安全存储
- ✅ 密码分类管理
- ✅ 密码搜索与筛选
- ✅ 密码强度检测
- ✅ 密码生成器
- ✅ TOTP二次验证管理

### 3. 密钥管理功能 (新增)
- ✅ AK/SK密钥对管理
- ✅ SSH私钥管理
- ✅ API密钥管理
- ✅ 数字证书管理
- ✅ 密钥使用审计
- ✅ 密钥过期提醒

### 4. 安全功能
- ✅ 端到端加密
- ✅ 零知识架构
- ✅ 主密码验证
- ✅ 二次验证(2FA)
- ✅ 操作审计日志

### 5. 数据管理功能
- ✅ 数据导入导出
- ✅ 数据备份恢复
- ✅ 数据同步
- ✅ 逻辑删除

---

## 🏗️ 系统架构设计

### DDD分层架构

```
securefox/
├── domain/                    # 领域层
│   ├── user/                 # 用户聚合
│   │   ├── User.java         # 聚合根
│   │   ├── UserRepository.java
│   │   └── UserDomainService.java
│   ├── vault/                # 密码库聚合
│   │   ├── Password.java     # 聚合根
│   │   ├── PasswordCategory.java
│   │   ├── PasswordRepository.java
│   │   └── VaultDomainService.java
│   ├── key/                  # 密钥管理聚合 (新增)
│   │   ├── Key.java          # 聚合根
│   │   ├── KeyType.java      # 密钥类型枚举
│   │   ├── EncryptedKeyData.java # 加密密钥数据值对象
│   │   ├── KeyMetadata.java  # 密钥元数据值对象
│   │   ├── KeyUsageLog.java  # 密钥使用日志实体
│   │   ├── KeyRepository.java # 密钥仓储接口
│   │   └── KeyDomainService.java # 密钥领域服务
│   └── security/             # 安全领域服务
│       ├── EncryptionService.java
│       └── MasterPasswordService.java
├── application/              # 应用服务层
│   ├── UserApplicationService.java
│   ├── VaultApplicationService.java
│   └── KeyApplicationService.java # 密钥应用服务 (新增)
├── infrastructure/           # 基础设施层
│   ├── repository/          # 仓储实现
│   │   ├── UserRepositoryImpl.java
│   │   ├── PasswordRepositoryImpl.java
│   │   └── KeyRepositoryImpl.java # 密钥仓储实现 (新增)
│   ├── security/            # 安全实现
│   └── config/              # 配置类
└── interfaces/              # 接口层
    └── rest/               # REST API
        ├── UserController.java
        ├── VaultController.java
        └── KeyController.java # 密钥控制器 (新增)
```

### 核心聚合设计

#### 1. 用户聚合 (User Aggregate)
- **聚合根**: User
- **实体**: UserProfile, LoginHistory
- **值对象**: Email, MasterPasswordHash
- **仓储**: UserRepository
- **领域服务**: UserDomainService

#### 2. 密码库聚合 (Vault Aggregate)
- **聚合根**: Password
- **实体**: PasswordEntry, PasswordHistory
- **值对象**: PasswordCategory, EncryptedData, TotpConfig
- **仓储**: PasswordRepository
- **领域服务**: VaultDomainService, TotpService

#### 3. 密钥管理聚合 (Key Aggregate) - 新增
- **聚合根**: Key
- **实体**: KeyUsageLog
- **值对象**: KeyType, EncryptedKeyData, KeyMetadata
- **仓储**: KeyRepository
- **领域服务**: KeyDomainService

#### 4. 安全领域服务
- **加密服务**: EncryptionService
- **主密码服务**: MasterPasswordService
- **审计服务**: AuditService

---

## 🔐 安全架构设计

### 加密算法选择
- **主密码哈希**: Argon2id (推荐参数: m=65536, t=3, p=4)
- **数据加密**: AES-256-GCM
- **密钥派生**: PBKDF2-SHA256 (迭代次数: 100,000)
- **随机数生成**: SecureRandom (密码学安全)

### 零知识架构实现
```
用户输入主密码 → Argon2哈希 → 存储哈希值
用户密码数据 → AES-256加密 → 存储密文
服务端永远无法获取用户明文数据
```

### 密钥管理流程
1. **主密码验证**: 用户输入 → Argon2验证 → 生成会话密钥
2. **数据加密**: 明文数据 → AES-256-GCM → 密文存储
3. **数据解密**: 密文数据 → AES-256-GCM → 明文返回

---

## 💾 数据库设计

### 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    master_password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);
```

#### 密码表 (passwords)
```sql
CREATE TABLE passwords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    encrypted_password TEXT NOT NULL,
    website VARCHAR(500),
    notes TEXT,
    category_id INTEGER,
    -- TOTP相关字段 (新增)
    totp_secret TEXT,                    -- 加密的TOTP密钥
    totp_issuer VARCHAR(255),            -- TOTP发行者
    totp_account_name VARCHAR(255),      -- TOTP账户名
    totp_digits INTEGER DEFAULT 6,      -- 验证码位数
    totp_period INTEGER DEFAULT 30,     -- 时间间隔(秒)
    totp_algorithm VARCHAR(10) DEFAULT 'SHA1', -- 算法
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### 分类表 (categories)
```sql
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    color VARCHAR(7) DEFAULT '#007AFF',
    icon VARCHAR(50) DEFAULT 'folder',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 密钥表 (keys) - 新增
```sql
CREATE TABLE keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    key_type VARCHAR(50) NOT NULL,
    encrypted_key_data TEXT NOT NULL,
    description TEXT,
    metadata TEXT,
    expires_at DATETIME,
    usage_count INTEGER DEFAULT 0,
    last_used_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 密钥使用日志表 (key_usage_logs) - 新增
```sql
CREATE TABLE key_usage_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (key_id) REFERENCES keys(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id INTEGER,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

---

## 🛠️ 技术栈详细配置

### Maven 依赖配置
```xml
<dependencies>
    <!-- Spring Boot Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- SA-Token 权限认证 -->
    <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot-starter</artifactId>
        <version>1.37.0</version>
    </dependency>
    
    <!-- MyBatis-Plus -->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.5</version>
    </dependency>
    
    <!-- SQLite JDBC -->
    <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>********</version>
    </dependency>
    
    <!-- 加密库 -->
    <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>1.70</version>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
    
    <!-- Validation -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!-- TOTP支持 (新增) -->
    <dependency>
        <groupId>com.warrenstrange</groupId>
        <artifactId>googleauth</artifactId>
        <version>1.5.0</version>
    </dependency>

    <!-- 二维码生成 (新增) -->
    <dependency>
        <groupId>com.google.zxing</groupId>
        <artifactId>core</artifactId>
        <version>3.5.2</version>
    </dependency>
    <dependency>
        <groupId>com.google.zxing</groupId>
        <artifactId>javase</artifactId>
        <version>3.5.2</version>
    </dependency>
</dependencies>
```

### Spring Boot 配置
```yaml
spring:
  application:
    name: securefox
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: *****************************
    username: 
    password: 
  
# SA-Token 配置
sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: 1800
  is-concurrent: true
  is-share: false
  max-login-count: 5

# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
```

---

## 🚀 开发实施计划

### Phase 1: 基础框架搭建 (1-2天)
- [x] 创建Spring Boot项目
- [x] 配置Maven依赖
- [x] 配置application.yml
- [x] 创建基础包结构

### Phase 2: 数据库设计 (1-2天)
- [ ] 设计数据库表结构
- [ ] 创建schema.sql
- [ ] 配置SQLite连接
- [ ] 测试数据库连接

### Phase 3: 领域层核心 (3-4天)
- [ ] 实现User聚合根
- [ ] 实现Password聚合根
- [ ] 定义仓储接口
- [ ] 实现核心领域服务

### Phase 4: 安全组件 (3-4天)
- [ ] 实现加密服务
- [ ] 实现主密码服务
- [ ] 配置SA-Token
- [ ] 实现认证逻辑

### Phase 5: 基础设施层 (2-3天)
- [ ] 实现MyBatis-Plus Mapper
- [ ] 实现仓储实现类
- [ ] 配置MyBatis-Plus
- [ ] 测试数据访问

### Phase 6: 应用服务层 (2-3天)
- [ ] 实现用户应用服务
- [ ] 实现密码库应用服务
- [ ] 实现业务逻辑
- [ ] 添加事务管理

### Phase 7: 接口层 (1-2天)
- [ ] 实现REST控制器
- [ ] 定义DTO对象
- [ ] 实现异常处理
- [ ] 添加API文档

### Phase 8: 密钥管理功能 (3-4天) - 新增
- [ ] 设计Key聚合根和相关值对象
- [ ] 实现KeyRepository接口和实现类
- [ ] 创建密钥相关数据库表
- [ ] 实现KeyApplicationService
- [ ] 开发KeyController REST接口
- [ ] 添加密钥使用审计功能

**总预估工作量**: 16-24天

---

## 📊 非功能性需求

### 性能要求
- **响应时间**: API响应时间 < 200ms
- **并发用户**: 支持1000+并发用户
- **数据库**: 支持10万+密码记录

### 安全要求
- **加密标准**: 符合OWASP密码存储指南
- **会话管理**: 支持会话超时和并发控制
- **审计日志**: 记录所有敏感操作

### 可扩展性
- **模块化设计**: 支持功能模块独立开发
- **数据库迁移**: 支持从SQLite迁移到MySQL/PostgreSQL
- **微服务拆分**: DDD架构便于后期微服务化

---

## 🔍 竞品分析总结

| 特性 | Bitwarden | 1Password | LastPass | SecureFox |
|------|-----------|-----------|----------|-----------|
| 开源 | ✅ | ❌ | ❌ | ✅ |
| 零知识架构 | ✅ | ✅ | ✅ | ✅ |
| 本地部署 | ✅ | ❌ | ❌ | ✅ |
| DDD架构 | ❌ | ❌ | ❌ | ✅ |
| 轻量级 | ❌ | ❌ | ❌ | ✅ |
| TOTP支持 | ✅ | ✅ | ✅ | ✅ |
| 密钥管理 | ❌ | ❌ | ❌ | ✅ |
| 使用审计 | ✅ | ✅ | ✅ | ✅ |

---

---

## 🎨 API接口设计

### 用户管理接口

#### 用户注册
```http
POST /api/v1/users/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}
```

#### 用户登录 (邮箱+主密码)
```http
POST /api/v1/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "masterPassword": "SecurePassword123!"
}
```

#### 获取用户信息
```http
GET /api/v1/users/profile
Authorization: Bearer {token}
```

### 密码管理接口

#### 创建密码
```http
POST /api/v1/passwords
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "GitHub",
  "username": "myusername",
  "password": "encrypted_password_data",
  "website": "https://github.com",
  "categoryId": 1,
  "notes": "Work account"
}
```

#### 获取密码列表
```http
GET /api/v1/passwords?page=1&size=20&category=1&search=github
Authorization: Bearer {token}
```

#### 更新密码
```http
PUT /api/v1/passwords/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "GitHub Updated",
  "username": "newusername",
  "password": "new_encrypted_password_data"
}
```

#### 删除密码
```http
DELETE /api/v1/passwords/{id}
Authorization: Bearer {token}
```

### TOTP管理接口 - 新增

#### 获取TOTP验证码
```http
GET /api/v1/passwords/{id}/totp
Authorization: Bearer {token}

Response:
{
  "code": "123456",
  "remainingSeconds": 25,
  "issuer": "GitHub",
  "accountName": "<EMAIL>"
}
```

#### 设置TOTP配置
```http
POST /api/v1/passwords/{id}/totp
Authorization: Bearer {token}
Content-Type: application/json

{
  "secret": "JBSWY3DPEHPK3PXP",
  "issuer": "GitHub",
  "accountName": "<EMAIL>",
  "digits": 6,
  "period": 30,
  "algorithm": "SHA1"
}
```

#### 通过二维码URL设置TOTP
```http
POST /api/v1/passwords/{id}/totp/qrcode
Authorization: Bearer {token}
Content-Type: application/json

{
  "qrcodeUrl": "otpauth://totp/GitHub:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=GitHub"
}
```

#### 获取TOTP二维码
```http
GET /api/v1/passwords/{id}/totp/qrcode
Authorization: Bearer {token}

Response:
{
  "qrcodeUrl": "otpauth://totp/GitHub:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=GitHub",
  "qrcodeImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
}
```

#### 删除TOTP配置
```http
DELETE /api/v1/passwords/{id}/totp
Authorization: Bearer {token}
```

### 分类管理接口

#### 创建分类
```http
POST /api/v1/categories
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Work",
  "color": "#FF6B6B",
  "icon": "briefcase"
}
```

#### 获取分类列表
```http
GET /api/v1/categories
Authorization: Bearer {token}
```

### 密钥管理接口 - 新增

#### 创建密钥
```http
POST /api/v1/keys
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "AWS Production Keys",
  "keyType": "ACCESS_KEY_SECRET",
  "keyData": {
    "accessKey": "AKIA...",
    "secretKey": "encrypted_secret_key"
  },
  "description": "生产环境AWS密钥",
  "expiresAt": "2025-12-31T23:59:59"
}
```

#### 获取密钥列表
```http
GET /api/v1/keys?type=SSH_PRIVATE_KEY&page=1&size=20
Authorization: Bearer {token}
```

#### 获取密钥详情
```http
GET /api/v1/keys/{id}
Authorization: Bearer {token}
```

#### 更新密钥
```http
PUT /api/v1/keys/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Updated Key Name",
  "description": "Updated description",
  "expiresAt": "2026-12-31T23:59:59"
}
```

#### 删除密钥
```http
DELETE /api/v1/keys/{id}
Authorization: Bearer {token}
```

#### 记录密钥使用
```http
POST /api/v1/keys/{id}/usage
Authorization: Bearer {token}
Content-Type: application/json

{
  "action": "DECRYPT",
  "description": "Used for SSH connection"
}
```

---

## 🔧 核心代码结构示例

### 密钥管理聚合示例 - 新增

#### Key聚合根
```java
@Entity
@TableName("keys")
@Data
@NoArgsConstructor
public class Key {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    @NotBlank
    private String name;

    @Enumerated(EnumType.STRING)
    private KeyType keyType;

    @NotBlank
    private String encryptedKeyData;

    private String description;

    private String metadata; // JSON格式存储

    private LocalDateTime expiresAt;

    private Integer usageCount = 0;

    private LocalDateTime lastUsedAt;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableLogic
    private Integer deleted;

    // 领域方法
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    public void recordUsage() {
        this.usageCount++;
        this.lastUsedAt = LocalDateTime.now();
    }

    public String decryptKeyData(EncryptionService encryptionService) {
        return encryptionService.decrypt(this.encryptedKeyData);
    }

    public void updateKeyData(String newKeyData, EncryptionService encryptionService) {
        this.encryptedKeyData = encryptionService.encrypt(newKeyData);
    }
}
```

#### KeyType枚举
```java
public enum KeyType {
    ACCESS_KEY_SECRET("AK/SK密钥对"),
    SSH_PRIVATE_KEY("SSH私钥"),
    API_KEY("API密钥"),
    JWT_SECRET("JWT密钥"),
    DATABASE_PASSWORD("数据库密码"),
    CERTIFICATE("数字证书"),
    GPG_KEY("GPG密钥"),
    OTHER("其他类型");

    private final String description;

    KeyType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
```

#### KeyApplicationService
```java
@Service
@Transactional
public class KeyApplicationService {

    private final KeyRepository keyRepository;
    private final EncryptionService encryptionService;
    private final AuditService auditService;

    public KeyDTO createKey(CreateKeyCommand command) {
        // 验证密钥名称唯一性
        if (keyRepository.existsByUserIdAndName(command.getUserId(), command.getName())) {
            throw new BusinessException("密钥名称已存在");
        }

        // 创建密钥
        Key key = new Key();
        key.setUserId(command.getUserId());
        key.setName(command.getName());
        key.setKeyType(command.getKeyType());
        key.setDescription(command.getDescription());
        key.setExpiresAt(command.getExpiresAt());

        // 加密密钥数据
        key.updateKeyData(command.getKeyData(), encryptionService);

        keyRepository.save(key);

        // 记录审计日志
        auditService.logKeyCreated(key);

        return KeyDTO.from(key);
    }

    public KeyDTO getKeyById(Long keyId, Long userId) {
        Key key = keyRepository.findByIdAndUserId(keyId, userId)
            .orElseThrow(() -> new BusinessException("密钥不存在"));

        if (key.isExpired()) {
            throw new BusinessException("密钥已过期");
        }

        // 记录使用日志
        key.recordUsage();
        keyRepository.save(key);

        // 记录审计日志
        auditService.logKeyAccessed(key);

        return KeyDTO.from(key);
    }

    public List<KeyDTO> listKeys(Long userId, KeyType keyType, Pageable pageable) {
        List<Key> keys = keyRepository.findByUserIdAndKeyType(userId, keyType, pageable);
        return keys.stream()
                  .map(KeyDTO::from)
                  .collect(Collectors.toList());
    }
}
```

---

## 🔧 原有核心代码结构示例

### 领域层示例

#### User聚合根
```java
@Entity
@TableName("users")
@Data
@NoArgsConstructor
public class User {
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank
    private String username;

    @Email
    private String email;

    private String masterPasswordHash;

    private String salt;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableLogic
    private Integer deleted;

    // 领域方法
    public boolean verifyMasterPassword(String inputPassword, EncryptionService encryptionService) {
        return encryptionService.verifyPassword(inputPassword, this.masterPasswordHash, this.salt);
    }

    public void changeMasterPassword(String newPassword, EncryptionService encryptionService) {
        this.salt = encryptionService.generateSalt();
        this.masterPasswordHash = encryptionService.hashPassword(newPassword, this.salt);
    }
}
```

#### Password聚合根
```java
@Entity
@TableName("passwords")
@Data
@NoArgsConstructor
public class Password {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    @NotBlank
    private String title;

    private String username;

    @NotBlank
    private String encryptedPassword;

    private String website;

    private String notes;

    private Long categoryId;

    // TOTP相关字段 (新增)
    private String totpSecret;

    private String totpIssuer;

    private String totpAccountName;

    private Integer totpDigits = 6;

    private Integer totpPeriod = 30;

    private String totpAlgorithm = "SHA1";

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableLogic
    private Integer deleted;

    // 领域方法
    public void updatePassword(String newPassword, EncryptionService encryptionService) {
        this.encryptedPassword = encryptionService.encrypt(newPassword);
    }

    public String decryptPassword(EncryptionService encryptionService) {
        return encryptionService.decrypt(this.encryptedPassword);
    }

    // TOTP相关领域方法 (新增)
    public boolean hasTotpEnabled() {
        return StringUtils.hasText(this.totpSecret);
    }

    public void enableTotp(TotpConfig totpConfig, EncryptionService encryptionService) {
        this.totpSecret = encryptionService.encrypt(totpConfig.getSecret());
        this.totpIssuer = totpConfig.getIssuer();
        this.totpAccountName = totpConfig.getAccountName();
        this.totpDigits = totpConfig.getDigits();
        this.totpPeriod = totpConfig.getPeriod();
        this.totpAlgorithm = totpConfig.getAlgorithm();
    }

    public void disableTotp() {
        this.totpSecret = null;
        this.totpIssuer = null;
        this.totpAccountName = null;
        this.totpDigits = 6;
        this.totpPeriod = 30;
        this.totpAlgorithm = "SHA1";
    }

    public TotpConfig getTotpConfig(EncryptionService encryptionService) {
        if (!hasTotpEnabled()) {
            return null;
        }

        return TotpConfig.builder()
                .secret(encryptionService.decrypt(this.totpSecret))
                .issuer(this.totpIssuer)
                .accountName(this.totpAccountName)
                .digits(this.totpDigits)
                .period(this.totpPeriod)
                .algorithm(this.totpAlgorithm)
                .build();
    }
}
```

#### TotpConfig值对象 - 新增
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TotpConfig {
    private String secret;          // TOTP密钥
    private String issuer;          // 发行者（如GitHub）
    private String accountName;     // 账户名
    private int digits = 6;         // 验证码位数
    private int period = 30;        // 时间间隔（秒）
    private String algorithm = "SHA1"; // 算法

    public String generateOtpAuthUrl() {
        return String.format(
            "otpauth://totp/%s:%s?secret=%s&issuer=%s&digits=%d&period=%d&algorithm=%s",
            issuer, accountName, secret, issuer, digits, period, algorithm
        );
    }

    public static TotpConfig fromOtpAuthUrl(String url) {
        // 解析otpauth://格式的URL
        // 实现省略...
        return new TotpConfig();
    }
}
```

#### TotpService接口 - 新增
```java
public interface TotpService {
    /**
     * 生成TOTP验证码
     */
    String generateCode(TotpConfig config);

    /**
     * 验证TOTP验证码
     */
    boolean validateCode(TotpConfig config, String code);

    /**
     * 获取当前验证码剩余有效时间（秒）
     */
    int getRemainingSeconds(TotpConfig config);

    /**
     * 生成二维码图片（Base64格式）
     */
    String generateQrCodeImage(String otpAuthUrl);

    /**
     * 生成TOTP密钥
     */
    String generateSecret();
}
```

### 应用服务层示例

#### 用户应用服务
```java
@Service
@Transactional
public class UserApplicationService {

    private final UserRepository userRepository;
    private final EncryptionService encryptionService;
    private final MasterPasswordService masterPasswordService;

    public UserDTO register(RegisterCommand command) {
        // 验证用户名和邮箱唯一性
        if (userRepository.existsByUsername(command.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        if (userRepository.existsByEmail(command.getEmail())) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建用户
        User user = new User();
        user.setUsername(command.getUsername());
        user.setEmail(command.getEmail());

        // 设置主密码
        String salt = encryptionService.generateSalt();
        String hashedPassword = encryptionService.hashPassword(command.getMasterPassword(), salt);
        user.setSalt(salt);
        user.setMasterPasswordHash(hashedPassword);

        userRepository.save(user);

        return UserDTO.from(user);
    }

    public LoginResult login(LoginCommand command) {
        User user = userRepository.findByEmail(command.getEmail())
            .orElseThrow(() -> new BusinessException("邮箱或密码错误"));

        if (!user.verifyMasterPassword(command.getMasterPassword(), encryptionService)) {
            throw new BusinessException("邮箱或密码错误");
        }

        // 生成访问令牌
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        return new LoginResult(token, UserDTO.from(user));
    }
}
```

#### VaultApplicationService - TOTP功能扩展
```java
@Service
@Transactional
public class VaultApplicationService {

    private final PasswordRepository passwordRepository;
    private final EncryptionService encryptionService;
    private final TotpService totpService;
    private final AuditService auditService;

    public TotpCodeDTO generateTotpCode(Long passwordId, Long userId) {
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
            .orElseThrow(() -> new BusinessException("密码记录不存在"));

        if (!password.hasTotpEnabled()) {
            throw new BusinessException("该密码记录未启用TOTP");
        }

        TotpConfig totpConfig = password.getTotpConfig(encryptionService);
        String code = totpService.generateCode(totpConfig);
        int remainingSeconds = totpService.getRemainingSeconds(totpConfig);

        // 记录审计日志
        auditService.logTotpCodeGenerated(password);

        return TotpCodeDTO.builder()
                .code(code)
                .remainingSeconds(remainingSeconds)
                .issuer(totpConfig.getIssuer())
                .accountName(totpConfig.getAccountName())
                .build();
    }

    public void enableTotp(Long passwordId, Long userId, TotpConfigCommand command) {
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
            .orElseThrow(() -> new BusinessException("密码记录不存在"));

        TotpConfig totpConfig = TotpConfig.builder()
                .secret(command.getSecret())
                .issuer(command.getIssuer())
                .accountName(command.getAccountName())
                .digits(command.getDigits())
                .period(command.getPeriod())
                .algorithm(command.getAlgorithm())
                .build();

        password.enableTotp(totpConfig, encryptionService);
        passwordRepository.save(password);

        // 记录审计日志
        auditService.logTotpEnabled(password);
    }

    public void enableTotpFromQrCode(Long passwordId, Long userId, String qrcodeUrl) {
        TotpConfig totpConfig = TotpConfig.fromOtpAuthUrl(qrcodeUrl);

        TotpConfigCommand command = TotpConfigCommand.builder()
                .secret(totpConfig.getSecret())
                .issuer(totpConfig.getIssuer())
                .accountName(totpConfig.getAccountName())
                .digits(totpConfig.getDigits())
                .period(totpConfig.getPeriod())
                .algorithm(totpConfig.getAlgorithm())
                .build();

        enableTotp(passwordId, userId, command);
    }

    public TotpQrCodeDTO getTotpQrCode(Long passwordId, Long userId) {
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
            .orElseThrow(() -> new BusinessException("密码记录不存在"));

        if (!password.hasTotpEnabled()) {
            throw new BusinessException("该密码记录未启用TOTP");
        }

        TotpConfig totpConfig = password.getTotpConfig(encryptionService);
        String otpAuthUrl = totpConfig.generateOtpAuthUrl();
        String qrcodeImage = totpService.generateQrCodeImage(otpAuthUrl);

        return TotpQrCodeDTO.builder()
                .qrcodeUrl(otpAuthUrl)
                .qrcodeImage(qrcodeImage)
                .build();
    }

    public void disableTotp(Long passwordId, Long userId) {
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
            .orElseThrow(() -> new BusinessException("密码记录不存在"));

        password.disableTotp();
        passwordRepository.save(password);

        // 记录审计日志
        auditService.logTotpDisabled(password);
    }
}
```

---

## 🛡️ 安全实现细节

### 加密服务实现
```java
@Service
public class EncryptionServiceImpl implements EncryptionService {

    private static final String ARGON2_ALGORITHM = "Argon2id";
    private static final int ARGON2_MEMORY = 65536; // 64MB
    private static final int ARGON2_ITERATIONS = 3;
    private static final int ARGON2_PARALLELISM = 4;

    @Override
    public String hashPassword(String password, String salt) {
        Argon2 argon2 = Argon2Factory.create(Argon2Types.ARGON2id);
        return argon2.hash(ARGON2_ITERATIONS, ARGON2_MEMORY, ARGON2_PARALLELISM,
                          password.toCharArray(), salt.getBytes());
    }

    @Override
    public boolean verifyPassword(String password, String hash, String salt) {
        Argon2 argon2 = Argon2Factory.create(Argon2Types.ARGON2id);
        return argon2.verify(hash, password.toCharArray());
    }

    @Override
    public String encrypt(String plaintext) {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(getEncryptionKey(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            byte[] iv = cipher.getIV();

            // 组合IV和密文
            byte[] encryptedData = new byte[iv.length + ciphertext.length];
            System.arraycopy(iv, 0, encryptedData, 0, iv.length);
            System.arraycopy(ciphertext, 0, encryptedData, iv.length, ciphertext.length);

            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            throw new SecurityException("加密失败", e);
        }
    }

    @Override
    public String decrypt(String ciphertext) {
        try {
            byte[] encryptedData = Base64.getDecoder().decode(ciphertext);

            // 提取IV和密文
            byte[] iv = new byte[12]; // GCM IV长度
            byte[] encrypted = new byte[encryptedData.length - 12];
            System.arraycopy(encryptedData, 0, iv, 0, 12);
            System.arraycopy(encryptedData, 12, encrypted, 0, encrypted.length);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(getEncryptionKey(), "AES");
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec);

            byte[] plaintext = cipher.doFinal(encrypted);
            return new String(plaintext, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new SecurityException("解密失败", e);
        }
    }

    private byte[] getEncryptionKey() {
        // 从当前用户会话中获取加密密钥
        // 实际实现中应该基于用户主密码派生
        return "your-32-byte-key-here-12345678".getBytes();
    }
}
```

---

## 📝 总结

SecureFox 项目采用现代化的DDD架构设计，结合Spring Boot生态系统的成熟技术栈，为用户提供安全、可靠、易用的密码管理解决方案。项目具有以下核心优势：

1. **安全第一**: 采用业界最佳的加密算法和零知识架构
2. **架构先进**: DDD设计模式确保代码质量和可维护性
3. **技术成熟**: Spring Boot + MyBatis-Plus 提供稳定的技术基础
4. **轻量级**: SQLite数据库适合个人和小团队使用
5. **功能完整**: 支持密码管理、密钥管理、使用审计等全方位功能
6. **可扩展**: 模块化设计支持功能扩展和技术升级

项目预计16-24天完成核心功能开发（包含密钥管理功能），后续可根据用户反馈持续迭代优化。

### 🔑 核心功能亮点

SecureFox 在传统密码管理器基础上，提供了全方位的数字身份安全管理功能：

#### 密钥管理功能
- **多类型支持**: AK/SK、SSH私钥、API密钥、数字证书等
- **使用审计**: 完整的密钥使用日志和统计
- **过期管理**: 密钥过期提醒和自动管理
- **安全存储**: 与密码相同的零知识加密架构
- **独立聚合**: 符合DDD设计原则的独立业务模块

#### TOTP二次验证功能 (新增)
- **标准兼容**: 完全兼容RFC 6238标准
- **多算法支持**: SHA1、SHA256、SHA512算法
- **二维码支持**: 扫描二维码快速导入TOTP配置
- **实时生成**: 30秒刷新的6位验证码
- **倒计时显示**: 显示验证码剩余有效时间
- **批量管理**: 支持多个账户的TOTP统一管理

---

**文档版本**: v3.0 (新增TOTP二次验证功能)
**最后更新**: 2025-01-30
**作者**: Claude 4.0 sonnet
**项目地址**: https://github.com/gclmit/securefox
