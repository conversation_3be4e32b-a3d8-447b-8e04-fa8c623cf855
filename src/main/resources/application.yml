# SecureFox 应用配置
spring:
  application:
    name: securefox
  
  # 数据源配置 - SQLite
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: *****************************
    username:
    password:

  # Liquibase配置
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true
    
  # Jackson JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# SA-Token 配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: 1800
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # 同一账号最大登录数量，-1代表不限 （只有在 is-concurrent=true, is-share=false 时此配置项才有意义）
  max-login-count: 5
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true

# MyBatis-Plus 配置
mybatis-plus:
  # 配置扫描通用枚举
  type-enums-package: club.gclmit.securefox.domain.*.enums
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启Mybatis二级缓存，默认为 true
    cache-enabled: false
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      # 主键类型
      id-type: auto
      # 字段策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_null

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 错误页面配置
  error:
    include-message: always
    include-binding-errors: always

# 日志配置
logging:
  level:
    club.gclmit.securefox: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/securefox.log
    max-history: 30
  logback:
    rollingpolicy:
      max-file-size: 10MB

# SecureFox 自定义配置
securefox:
  # 安全配置
  security:
    # 主密码最小长度
    master-password-min-length: 8
    # 密码强度要求
    password-strength-required: true
    # 会话超时时间（分钟）
    session-timeout: 30
    # 最大登录失败次数
    max-login-attempts: 5
    # 账户锁定时间（分钟）
    account-lockout-duration: 15
    # 密码泄露检查配置
    breach-check:
      # 是否启用密码泄露检查
      enabled: true
      # HaveIBeenPwned API地址
      api-url: https://api.pwnedpasswords.com/range/
      # 请求超时时间（毫秒）
      timeout: 5000
  
  # 加密配置
  encryption:
    # AES密钥长度
    aes-key-length: 256
    # Argon2 配置
    argon2:
      memory: 65536
      iterations: 3
      parallelism: 4
    # PBKDF2 配置
    pbkdf2:
      iterations: 100000
      key-length: 256
  
  # TOTP配置
  totp:
    # 默认验证码位数
    default-digits: 6
    # 默认时间间隔（秒）
    default-period: 30
    # 默认算法
    default-algorithm: SHA1
    # 时间容差（允许前后多少个时间窗口）
    time-tolerance: 1
  
  # 数据库配置
  database:
    # 数据库文件路径
    file-path: data/securefox.db
    # 是否自动创建表
    auto-create-tables: true
    # 是否初始化数据
    init-data: true
  
  # 审计配置
  audit:
    # 是否启用审计日志
    enabled: true
    # 审计日志保留天数
    retention-days: 90
    # 是否记录敏感操作
    log-sensitive-operations: true

