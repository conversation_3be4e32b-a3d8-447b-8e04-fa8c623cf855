-- =====================================================
-- 003-create-passwords-table.sql
-- 创建密码表
-- Author: claude-4.0-sonnet
-- =====================================================

-- 创建密码表
CREATE TABLE IF NOT EXISTS passwords (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    category_id INTEGER,
    title VARCHAR(200) NOT NULL,
    username VARCHAR(255),
    encrypted_password TEXT NOT NULL,
    website VARCHAR(500),
    notes TEXT,
    totp_secret TEXT,
    totp_issuer VARCHAR(100),
    totp_account_name VARCHAR(100),
    totp_digits INTEGER DEFAULT 6,
    totp_period INTEGER DEFAULT 30,
    totp_algorithm VARCHAR(20) DEFAULT 'SHA1',
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_modified_device VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER NOT NULL DEFAULT 0,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES password_categories(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_passwords_user_id ON passwords(user_id);
CREATE INDEX IF NOT EXISTS idx_passwords_category_id ON passwords(category_id);
CREATE INDEX IF NOT EXISTS idx_passwords_title ON passwords(title);
CREATE INDEX IF NOT EXISTS idx_passwords_website ON passwords(website);
CREATE INDEX IF NOT EXISTS idx_passwords_deleted ON passwords(deleted);
CREATE INDEX IF NOT EXISTS idx_passwords_sync_version ON passwords(sync_version);

-- 创建更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_passwords_updated_at
    AFTER UPDATE ON passwords
    FOR EACH ROW
BEGIN
    UPDATE passwords SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 表说明：
-- - id: 主键，自增长
-- - user_id: 用户ID，外键关联users表
-- - category_id: 分类ID，外键关联password_categories表
-- - title: 密码项标题
-- - username: 用户名/账号
-- - encrypted_password: 加密后的密码（AES-256-GCM）
-- - website: 网站URL
-- - notes: 备注信息
-- - totp_secret: TOTP密钥（加密存储）
-- - totp_issuer: TOTP发行者
-- - totp_account_name: TOTP账户名
-- - totp_digits: TOTP验证码位数（默认6位）
-- - totp_period: TOTP时间间隔（默认30秒）
-- - totp_algorithm: TOTP算法（默认SHA1）
-- - sync_version: 同步版本号
-- - last_modified_device: 最后修改设备
-- - created_at: 创建时间
-- - updated_at: 更新时间
-- - deleted: 逻辑删除标记
