-- =====================================================
-- 005-create-audit-logs-table.sql
-- 创建审计日志表
-- Author: claude-4.0-sonnet
-- =====================================================

-- 创建审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id INTEGER,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON audit_logs(resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);

-- 表说明：
-- - id: 主键，自增长
-- - user_id: 用户ID，外键关联users表
-- - action: 操作类型（LOGIN, LOGOUT, CREATE_PASSWORD, UPDATE_PASSWORD, DELETE_PASSWORD, VIEW_PASSWORD, CREATE_NOTE, UPDATE_NOTE, DELETE_NOTE, VIEW_NOTE, EXPORT_DATA, IMPORT_DATA, CHANGE_MASTER_PASSWORD, ENABLE_TOTP, DISABLE_TOTP, USE_TOTP）
-- - resource_type: 资源类型（USER, PASSWORD, NOTE, CATEGORY, SYSTEM）
-- - resource_id: 资源ID
-- - details: 操作详情（JSON格式）
-- - ip_address: IP地址
-- - user_agent: 用户代理
-- - created_at: 创建时间
