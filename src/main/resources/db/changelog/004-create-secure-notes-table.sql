-- =====================================================
-- 004-create-secure-notes-table.sql
-- 创建安全笔记表
-- Author: claude-4.0-sonnet
-- =====================================================

-- 创建安全笔记表
CREATE TABLE IF NOT EXISTS secure_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    category_id INTEGER,
    title VARCHAR(200) NOT NULL,
    encrypted_content TEXT NOT NULL,
    note_type VARCHAR(50) NOT NULL,
    tags TEXT,
    favorite INTEGER NOT NULL DEFAULT 0,
    pinned INTEGER NOT NULL DEFAULT 0,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_modified_device VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER NOT NULL DEFAULT 0,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (category_id) REFERENCES password_categories(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_secure_notes_user_id ON secure_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_secure_notes_category_id ON secure_notes(category_id);
CREATE INDEX IF NOT EXISTS idx_secure_notes_title ON secure_notes(title);
CREATE INDEX IF NOT EXISTS idx_secure_notes_note_type ON secure_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_secure_notes_favorite ON secure_notes(favorite);
CREATE INDEX IF NOT EXISTS idx_secure_notes_pinned ON secure_notes(pinned);
CREATE INDEX IF NOT EXISTS idx_secure_notes_deleted ON secure_notes(deleted);
CREATE INDEX IF NOT EXISTS idx_secure_notes_sync_version ON secure_notes(sync_version);

-- 创建更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_secure_notes_updated_at
    AFTER UPDATE ON secure_notes
    FOR EACH ROW
BEGIN
    UPDATE secure_notes SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 表说明：
-- - id: 主键，自增长
-- - user_id: 用户ID，外键关联users表
-- - category_id: 分类ID，外键关联password_categories表
-- - title: 笔记标题
-- - encrypted_content: 加密后的笔记内容（AES-256-GCM）
-- - note_type: 笔记类型（GENERAL, PERSONAL_INFO, BANK_CARD, ID_CARD, SOFTWARE_LICENSE, SERVER_INFO, NETWORK_CONFIG, IMPORTANT_DOCUMENT, BACKUP_RECOVERY_CODE, SECURITY_QUESTION_ANSWER, ENCRYPTION_KEY, OTHER_SENSITIVE）
-- - tags: 标签（JSON格式存储）
-- - favorite: 是否收藏（0=否，1=是）
-- - pinned: 是否置顶（0=否，1=是）
-- - sync_version: 同步版本号
-- - last_modified_device: 最后修改设备
-- - created_at: 创建时间
-- - updated_at: 更新时间
-- - deleted: 逻辑删除标记
