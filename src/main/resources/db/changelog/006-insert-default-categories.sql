-- =====================================================
-- 006-insert-default-categories.sql
-- 插入默认分类数据
-- Author: claude-4.0-sonnet
-- =====================================================

-- 插入默认分类数据
-- 注意：这些是系统级默认分类，user_id为0表示系统分类

-- 社交媒体
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '社交媒体', '社交网络和通讯应用', 'users', '#3B82F6', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 工作
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '工作', '工作相关的账户和应用', 'briefcase', '#10B981', 2, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 金融
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '金融', '银行、支付和投资平台', 'credit-card', '#F59E0B', 3, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 购物
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '购物', '电商和购物网站', 'shopping-cart', '#EF4444', 4, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 娱乐
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '娱乐', '游戏、视频和音乐平台', 'play', '#8B5CF6', 5, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 教育
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '教育', '学习和教育平台', 'book', '#06B6D4', 6, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 开发
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '开发', '开发工具和代码托管', 'code', '#84CC16', 7, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 其他
INSERT OR IGNORE INTO password_categories (
    user_id, name, description, icon, color, sort_order, is_default, created_at, updated_at
) VALUES (
    0, '其他', '其他未分类的账户', 'folder', '#6B7280', 8, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

-- 数据说明：
-- - user_id = 0: 表示系统级默认分类，所有用户都可以使用
-- - is_default = 1: 标记为默认分类
-- - sort_order: 分类显示顺序
-- - icon: 图标名称（使用 Heroicons 或 Feather Icons）
-- - color: 分类颜色（Tailwind CSS 颜色值）
