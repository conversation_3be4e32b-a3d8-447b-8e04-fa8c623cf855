-- =====================================================
-- 001-create-users-table.sql
-- 创建用户表
-- Author: claude-4.0-sonnet
-- =====================================================

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    password_salt VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_deleted ON users(deleted);

-- 创建更新时间触发器（SQLite 自动更新 updated_at）
CREATE TRIGGER IF NOT EXISTS trigger_users_updated_at
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 插入说明注释
-- 表说明：
-- - id: 主键，自增长
-- - username: 用户名，唯一约束
-- - email: 邮箱，唯一约束  
-- - password_hash: 密码哈希值（Argon2id）
-- - password_salt: 密码盐值
-- - created_at: 创建时间，自动填充
-- - updated_at: 更新时间，自动更新
-- - deleted: 逻辑删除标记（0=未删除，1=已删除）
