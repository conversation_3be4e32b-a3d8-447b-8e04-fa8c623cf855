-- =====================================================
-- 002-create-password-categories-table.sql
-- 创建密码分类表
-- Author: claude-4.0-sonnet
-- =====================================================

-- 创建密码分类表
CREATE TABLE IF NOT EXISTS password_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    icon VARCHAR(50),
    color VARCHAR(20),
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_default INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER NOT NULL DEFAULT 0,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_password_categories_user_id ON password_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_password_categories_deleted ON password_categories(deleted);
CREATE INDEX IF NOT EXISTS idx_password_categories_sort_order ON password_categories(sort_order);

-- 创建更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_password_categories_updated_at
    AFTER UPDATE ON password_categories
    FOR EACH ROW
BEGIN
    UPDATE password_categories SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 表说明：
-- - id: 主键，自增长
-- - user_id: 用户ID，外键关联users表
-- - name: 分类名称
-- - description: 分类描述
-- - icon: 分类图标
-- - color: 分类颜色
-- - sort_order: 排序顺序
-- - is_default: 是否为默认分类（0=否，1=是）
-- - created_at: 创建时间
-- - updated_at: 更新时间
-- - deleted: 逻辑删除标记
