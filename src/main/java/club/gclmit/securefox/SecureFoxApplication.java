package club.gclmit.securefox;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * SecureFox 应用启动类
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Slf4j
@SpringBootApplication
@EnableTransactionManagement
@MapperScan("club.gclmit.securefox.infrastructure.repository.mapper")
public class SecureFoxApplication {

    public static void main(String[] args) {
        SpringApplication.run(SecureFoxApplication.class, args);
        log.info("🦊 SecureFox 启动完成！");
    }
}
