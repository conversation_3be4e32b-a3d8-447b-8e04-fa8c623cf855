package club.gclmit.securefox.application.service;

import club.gclmit.securefox.application.command.LoginCommand;
import club.gclmit.securefox.application.command.RegisterUserCommand;
import club.gclmit.securefox.application.dto.UserDTO;
import club.gclmit.securefox.domain.audit.AuditService;
import club.gclmit.securefox.domain.user.User;
import club.gclmit.securefox.domain.user.UserDomainService;
import club.gclmit.securefox.domain.user.UserRepository;
import club.gclmit.securefox.domain.vault.VaultDomainService;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 用户应用服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
@Transactional
public class UserApplicationService {
    
    private final UserRepository userRepository;
    private final UserDomainService userDomainService;
    private final VaultDomainService vaultDomainService;
    private final AuditService auditService;
    
    public UserApplicationService(UserRepository userRepository,
                                 UserDomainService userDomainService,
                                 VaultDomainService vaultDomainService,
                                 AuditService auditService) {
        this.userRepository = userRepository;
        this.userDomainService = userDomainService;
        this.vaultDomainService = vaultDomainService;
        this.auditService = auditService;
    }
    
    /**
     * 用户注册
     * 
     * @param command 注册命令
     * @return 用户DTO
     */
    public UserDTO register(RegisterUserCommand command) {
        // 创建用户
        User user = userDomainService.createUser(
            command.getUsername(),
            command.getEmail(),
            command.getMasterPassword()
        );
        
        // 为新用户创建默认分类
        vaultDomainService.createDefaultCategories(user.getId());
        
        // 记录审计日志
        auditService.logUserRegister(user, getCurrentIpAddress(), getCurrentUserAgent());
        
        return UserDTO.from(user);
    }
    
    /**
     * 用户登录
     * 
     * @param command 登录命令
     * @return 登录结果
     */
    public LoginResult login(LoginCommand command) {
        // 验证用户凭据
        User user = userDomainService.authenticateUser(command.getEmail(), command.getMasterPassword());
        if (user == null) {
            throw new IllegalArgumentException("邮箱或密码错误");
        }
        
        // 执行登录
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        
        // 更新最后登录时间
        userRepository.updateLastLoginTime(user.getId());
        
        // 记录审计日志
        auditService.logUserLogin(user, getCurrentIpAddress(), getCurrentUserAgent());
        
        return new LoginResult(token, UserDTO.from(user));
    }
    
    /**
     * 用户登出
     */
    public void logout() {
        StpUtil.logout();
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户DTO
     */
    @Transactional(readOnly = true)
    public UserDTO getCurrentUser() {
        Long userId = getCurrentUserId();
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));
        
        return UserDTO.from(user);
    }
    
    /**
     * 更改主密码
     * 
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    public boolean changeMasterPassword(String oldPassword, String newPassword) {
        Long userId = getCurrentUserId();
        return userDomainService.changeMasterPassword(userId, oldPassword, newPassword);
    }
    
    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 密码强度信息
     */
    @Transactional(readOnly = true)
    public PasswordStrengthInfo checkPasswordStrength(String password) {
        int strength = userDomainService.checkPasswordStrength(password);
        String description = userDomainService.getPasswordStrengthDescription(password);
        
        return new PasswordStrengthInfo(strength, description);
    }
    
    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    public Long getCurrentUserId() {
        return StpUtil.getLoginIdAsLong();
    }
    
    /**
     * 获取当前请求的IP地址
     */
    private String getCurrentIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                    return xForwardedFor.split(",")[0].trim();
                }
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "unknown";
    }
    
    /**
     * 获取当前请求的User-Agent
     */
    private String getCurrentUserAgent() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "unknown";
    }
    
    /**
     * 登录结果
     */
    public static class LoginResult {
        private final String token;
        private final UserDTO user;
        
        public LoginResult(String token, UserDTO user) {
            this.token = token;
            this.user = user;
        }
        
        public String getToken() { return token; }
        public UserDTO getUser() { return user; }
    }
    
    /**
     * 密码强度信息
     */
    public static class PasswordStrengthInfo {
        private final int strength;
        private final String description;
        
        public PasswordStrengthInfo(int strength, String description) {
            this.strength = strength;
            this.description = description;
        }
        
        public int getStrength() { return strength; }
        public String getDescription() { return description; }
    }
}
