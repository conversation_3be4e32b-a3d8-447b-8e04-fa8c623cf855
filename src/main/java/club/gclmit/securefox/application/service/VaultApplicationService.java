package club.gclmit.securefox.application.service;

import club.gclmit.securefox.application.command.CreatePasswordCommand;
import club.gclmit.securefox.application.dto.PasswordDTO;
import club.gclmit.securefox.domain.audit.AuditService;
import club.gclmit.securefox.domain.security.EncryptionService;
import club.gclmit.securefox.domain.security.TotpService;
import club.gclmit.securefox.domain.vault.*;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 密码库应用服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
@Transactional
public class VaultApplicationService {
    
    private final PasswordRepository passwordRepository;
    private final PasswordCategoryRepository categoryRepository;
    private final VaultDomainService vaultDomainService;
    private final EncryptionService encryptionService;
    private final TotpService totpService;
    private final AuditService auditService;
    
    public VaultApplicationService(PasswordRepository passwordRepository,
                                  PasswordCategoryRepository categoryRepository,
                                  VaultDomainService vaultDomainService,
                                  EncryptionService encryptionService,
                                  TotpService totpService,
                                  AuditService auditService) {
        this.passwordRepository = passwordRepository;
        this.categoryRepository = categoryRepository;
        this.vaultDomainService = vaultDomainService;
        this.encryptionService = encryptionService;
        this.totpService = totpService;
        this.auditService = auditService;
    }
    
    /**
     * 创建密码
     * 
     * @param command 创建密码命令
     * @return 密码DTO
     */
    public PasswordDTO createPassword(CreatePasswordCommand command) {
        Long userId = getCurrentUserId();
        
        Password password = vaultDomainService.createPassword(
            userId,
            command.getTitle(),
            command.getUsername(),
            command.getPassword(),
            command.getWebsite(),
            command.getCategoryId(),
            command.getNotes()
        );
        
        // 记录审计日志
        auditService.logPasswordCreated(password);
        
        return PasswordDTO.from(password);
    }
    
    /**
     * 获取用户所有密码
     * 
     * @return 密码列表
     */
    @Transactional(readOnly = true)
    public List<PasswordDTO> getUserPasswords() {
        Long userId = getCurrentUserId();
        List<Password> passwords = passwordRepository.findByUserId(userId);
        
        return passwords.stream()
                .map(PasswordDTO::from)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据ID获取密码
     * 
     * @param passwordId 密码ID
     * @return 密码DTO
     */
    @Transactional(readOnly = true)
    public PasswordDTO getPassword(Long passwordId) {
        Long userId = getCurrentUserId();
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
                .orElseThrow(() -> new IllegalArgumentException("密码不存在"));
        
        return PasswordDTO.from(password);
    }
    
    /**
     * 获取密码明文
     * 
     * @param passwordId 密码ID
     * @return 明文密码
     */
    @Transactional(readOnly = true)
    public String getPasswordPlaintext(Long passwordId) {
        Long userId = getCurrentUserId();
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
                .orElseThrow(() -> new IllegalArgumentException("密码不存在"));
        
        // 记录审计日志
        auditService.logPasswordViewed(password);
        
        return password.decryptPassword(encryptionService);
    }
    
    /**
     * 更新密码
     * 
     * @param passwordId 密码ID
     * @param command 更新命令
     * @return 密码DTO
     */
    public PasswordDTO updatePassword(Long passwordId, CreatePasswordCommand command) {
        Long userId = getCurrentUserId();
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
                .orElseThrow(() -> new IllegalArgumentException("密码不存在"));
        
        // 更新基本信息
        password.setTitle(command.getTitle());
        password.setUsername(command.getUsername());
        password.setWebsite(command.getWebsite());
        password.setNotes(command.getNotes());
        password.setCategoryId(command.getCategoryId());
        
        // 更新密码
        if (command.getPassword() != null && !command.getPassword().isEmpty()) {
            password.updatePassword(command.getPassword(), encryptionService);
        }
        
        passwordRepository.save(password);
        
        // 记录审计日志
        auditService.logPasswordUpdated(password);
        
        return PasswordDTO.from(password);
    }
    
    /**
     * 删除密码
     * 
     * @param passwordId 密码ID
     */
    public void deletePassword(Long passwordId) {
        Long userId = getCurrentUserId();
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
                .orElseThrow(() -> new IllegalArgumentException("密码不存在"));
        
        passwordRepository.deleteById(passwordId);
        
        // 记录审计日志
        auditService.logPasswordDeleted(password);
    }
    
    /**
     * 搜索密码
     * 
     * @param keyword 关键词
     * @return 密码列表
     */
    @Transactional(readOnly = true)
    public List<PasswordDTO> searchPasswords(String keyword) {
        Long userId = getCurrentUserId();
        List<Password> passwords = vaultDomainService.searchPasswords(userId, keyword);
        
        return passwords.stream()
                .map(PasswordDTO::from)
                .collect(Collectors.toList());
    }
    
    /**
     * 启用TOTP
     * 
     * @param passwordId 密码ID
     * @param totpConfig TOTP配置
     * @return 是否成功
     */
    public boolean enableTotp(Long passwordId, TotpConfig totpConfig) {
        Long userId = getCurrentUserId();
        boolean success = vaultDomainService.enableTotpForPassword(passwordId, userId, totpConfig);
        
        if (success) {
            Password password = passwordRepository.findByIdAndUserId(passwordId, userId).orElse(null);
            if (password != null) {
                auditService.logTotpEnabled(password);
            }
        }
        
        return success;
    }
    
    /**
     * 禁用TOTP
     * 
     * @param passwordId 密码ID
     * @return 是否成功
     */
    public boolean disableTotp(Long passwordId) {
        Long userId = getCurrentUserId();
        Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
                .orElseThrow(() -> new IllegalArgumentException("密码不存在"));
        
        password.disableTotp();
        passwordRepository.save(password);
        
        // 记录审计日志
        auditService.logTotpDisabled(password);
        
        return true;
    }
    
    /**
     * 生成TOTP验证码
     * 
     * @param passwordId 密码ID
     * @return TOTP验证码信息
     */
    @Transactional(readOnly = true)
    public TotpCodeInfo generateTotpCode(Long passwordId) {
        Long userId = getCurrentUserId();
        VaultDomainService.TotpCodeInfo codeInfo = vaultDomainService.generateTotpCode(passwordId, userId);
        
        if (codeInfo != null) {
            Password password = passwordRepository.findByIdAndUserId(passwordId, userId).orElse(null);
            if (password != null) {
                auditService.logTotpCodeGenerated(password);
            }
            
            return new TotpCodeInfo(
                codeInfo.getCode(),
                codeInfo.getRemainingSeconds(),
                codeInfo.getIssuer(),
                codeInfo.getAccountName()
            );
        }
        
        return null;
    }
    
    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 密码强度等级
     */
    @Transactional(readOnly = true)
    public int checkPasswordStrength(String password) {
        return vaultDomainService.checkPasswordStrength(password);
    }
    
    /**
     * 生成安全密码
     * 
     * @param length 密码长度
     * @param includeUppercase 包含大写字母
     * @param includeLowercase 包含小写字母
     * @param includeNumbers 包含数字
     * @param includeSymbols 包含特殊字符
     * @return 生成的密码
     */
    @Transactional(readOnly = true)
    public String generateSecurePassword(int length, boolean includeUppercase,
                                       boolean includeLowercase, boolean includeNumbers,
                                       boolean includeSymbols) {
        return vaultDomainService.generateSecurePassword(length, includeUppercase,
                                                        includeLowercase, includeNumbers, includeSymbols);
    }
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        return StpUtil.getLoginIdAsLong();
    }
    
    /**
     * TOTP验证码信息
     */
    public static class TotpCodeInfo {
        private final String code;
        private final int remainingSeconds;
        private final String issuer;
        private final String accountName;
        
        public TotpCodeInfo(String code, int remainingSeconds, String issuer, String accountName) {
            this.code = code;
            this.remainingSeconds = remainingSeconds;
            this.issuer = issuer;
            this.accountName = accountName;
        }
        
        public String getCode() { return code; }
        public int getRemainingSeconds() { return remainingSeconds; }
        public String getIssuer() { return issuer; }
        public String getAccountName() { return accountName; }
    }
}
