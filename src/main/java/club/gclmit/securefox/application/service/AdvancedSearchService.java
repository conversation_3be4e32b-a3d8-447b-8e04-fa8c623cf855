package club.gclmit.securefox.application.service;

import club.gclmit.securefox.application.dto.AdvancedSearchQuery;
import club.gclmit.securefox.application.dto.PasswordDTO;
import club.gclmit.securefox.application.dto.SearchResult;
import club.gclmit.securefox.application.dto.SecureNoteDTO;
import club.gclmit.securefox.domain.note.NoteType;
import club.gclmit.securefox.domain.note.SecureNote;
import club.gclmit.securefox.domain.note.SecureNoteRepository;
import club.gclmit.securefox.domain.vault.Password;
import club.gclmit.securefox.domain.vault.PasswordRepository;
import club.gclmit.securefox.domain.vault.VaultDomainService;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 高级搜索服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvancedSearchService {
    
    private final PasswordRepository passwordRepository;
    private final SecureNoteRepository noteRepository;
    private final VaultDomainService vaultDomainService;
    
    // 搜索历史缓存
    private final Map<Long, List<String>> userSearchHistory = new HashMap<>();
    
    /**
     * 高级搜索密码
     * 
     * @param query 搜索查询
     * @return 搜索结果
     */
    @Transactional(readOnly = true)
    public SearchResult<PasswordDTO> searchPasswords(AdvancedSearchQuery query) {
        long startTime = System.currentTimeMillis();
        Long userId = getCurrentUserId();
        
        // 记录搜索历史
        recordSearchHistory(userId, query.getKeyword());
        
        try {
            // 获取所有密码
            List<Password> allPasswords = passwordRepository.findByUserId(userId);
            
            // 应用搜索条件
            List<Password> filteredPasswords = applyPasswordFilters(allPasswords, query);
            
            // 应用排序
            List<Password> sortedPasswords = applyPasswordSorting(filteredPasswords, query);
            
            // 应用分页
            List<Password> pagedPasswords = applyPagination(sortedPasswords, query.getPageRequest());
            
            // 转换为DTO
            List<PasswordDTO> passwordDTOs = pagedPasswords.stream()
                .map(PasswordDTO::from)
                .collect(Collectors.toList());
            
            // 创建搜索结果
            SearchResult<PasswordDTO> result = SearchResult.of(passwordDTOs, filteredPasswords.size(), query.getPageRequest());
            result.setSearchTimeMs(System.currentTimeMillis() - startTime);
            
            // 添加统计信息
            result.setStatistics(buildPasswordStatistics(filteredPasswords));
            
            // 添加搜索建议
            result.setSuggestions(getPasswordSearchSuggestions(query.getKeyword(), userId));
            
            return result;
            
        } catch (Exception e) {
            log.error("密码搜索失败", e);
            SearchResult<PasswordDTO> errorResult = SearchResult.empty(query.getPageRequest());
            errorResult.setSearchTimeMs(System.currentTimeMillis() - startTime);
            return errorResult;
        }
    }
    
    /**
     * 高级搜索笔记
     * 
     * @param query 搜索查询
     * @return 搜索结果
     */
    @Transactional(readOnly = true)
    public SearchResult<SecureNoteDTO> searchNotes(AdvancedSearchQuery query) {
        long startTime = System.currentTimeMillis();
        Long userId = getCurrentUserId();
        
        // 记录搜索历史
        recordSearchHistory(userId, query.getKeyword());
        
        try {
            // 获取所有笔记
            List<SecureNote> allNotes = noteRepository.findByUserId(userId);
            
            // 应用搜索条件
            List<SecureNote> filteredNotes = applyNoteFilters(allNotes, query);
            
            // 应用排序
            List<SecureNote> sortedNotes = applyNoteSorting(filteredNotes, query);
            
            // 应用分页
            List<SecureNote> pagedNotes = applyPagination(sortedNotes, query.getPageRequest());
            
            // 转换为DTO
            List<SecureNoteDTO> noteDTOs = pagedNotes.stream()
                .map(SecureNoteDTO::from)
                .collect(Collectors.toList());
            
            // 创建搜索结果
            SearchResult<SecureNoteDTO> result = SearchResult.of(noteDTOs, filteredNotes.size(), query.getPageRequest());
            result.setSearchTimeMs(System.currentTimeMillis() - startTime);
            
            // 添加统计信息
            result.setStatistics(buildNoteStatistics(filteredNotes));
            
            // 添加搜索建议
            result.setSuggestions(getNoteSearchSuggestions(query.getKeyword(), userId));
            
            return result;
            
        } catch (Exception e) {
            log.error("笔记搜索失败", e);
            SearchResult<SecureNoteDTO> errorResult = SearchResult.empty(query.getPageRequest());
            errorResult.setSearchTimeMs(System.currentTimeMillis() - startTime);
            return errorResult;
        }
    }
    
    /**
     * 获取搜索建议
     * 
     * @param partialQuery 部分查询
     * @param userId 用户ID
     * @return 搜索建议列表
     */
    public List<SearchResult.SearchSuggestion> getSearchSuggestions(String partialQuery, Long userId) {
        List<SearchResult.SearchSuggestion> suggestions = new ArrayList<>();
        
        if (partialQuery == null || partialQuery.trim().isEmpty()) {
            return suggestions;
        }
        
        String query = partialQuery.toLowerCase().trim();
        
        // 历史搜索建议
        suggestions.addAll(getHistoricalSuggestions(query, userId));
        
        // 内容匹配建议
        suggestions.addAll(getContentSuggestions(query, userId));
        
        // 分类建议
        suggestions.addAll(getCategorySuggestions(query, userId));
        
        // 标签建议
        suggestions.addAll(getTagSuggestions(query, userId));
        
        // 网站建议
        suggestions.addAll(getWebsiteSuggestions(query, userId));
        
        return suggestions.stream()
            .sorted(Comparator.comparing(SearchResult.SearchSuggestion::getRelevanceScore).reversed())
            .limit(10)
            .collect(Collectors.toList());
    }
    
    /**
     * 异步重建搜索索引
     * 
     * @param userId 用户ID
     * @return 异步结果
     */
    @Async("searchExecutor")
    public CompletableFuture<Void> rebuildSearchIndex(Long userId) {
        log.info("开始重建用户 {} 的搜索索引", userId);
        
        try {
            // 这里可以实现更复杂的索引重建逻辑
            // 例如：构建倒排索引、计算TF-IDF等
            
            List<Password> passwords = passwordRepository.findByUserId(userId);
            List<SecureNote> notes = noteRepository.findByUserId(userId);
            
            log.info("用户 {} 的搜索索引重建完成，密码数量: {}, 笔记数量: {}", 
                    userId, passwords.size(), notes.size());
            
        } catch (Exception e) {
            log.error("重建搜索索引失败", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    // 私有方法实现
    
    private List<Password> applyPasswordFilters(List<Password> passwords, AdvancedSearchQuery query) {
        return passwords.stream()
            .filter(password -> matchesKeyword(password, query.getKeyword()))
            .filter(password -> matchesCategories(password.getCategoryId(), query.getCategoryIds()))
            .filter(password -> matchesWebsites(password.getWebsite(), query.getWebsites()))
            .filter(password -> matchesTotp(password, query.getHasTotp()))
            .filter(password -> matchesStrengthRange(password, query.getStrengthRange()))
            .filter(password -> matchesDateRange(password.getCreatedAt(), query.getDateRange()))
            .filter(password -> matchesUpdateDateRange(password.getUpdatedAt(), query.getUpdateDateRange()))
            .filter(password -> matchesDeleted(password.getDeleted(), query.isIncludeDeleted()))
            .collect(Collectors.toList());
    }
    
    private List<SecureNote> applyNoteFilters(List<SecureNote> notes, AdvancedSearchQuery query) {
        return notes.stream()
            .filter(note -> matchesKeyword(note, query.getKeyword()))
            .filter(note -> matchesCategories(note.getCategoryId(), query.getCategoryIds()))
            .filter(note -> matchesNoteTypes(note.getNoteType(), query.getNoteTypes()))
            .filter(note -> matchesTags(note.getTags(), query.getTags()))
            .filter(note -> matchesFavorite(note.getFavorite(), query.getFavorite()))
            .filter(note -> matchesPinned(note.getPinned(), query.getPinned()))
            .filter(note -> matchesDateRange(note.getCreatedAt(), query.getDateRange()))
            .filter(note -> matchesUpdateDateRange(note.getUpdatedAt(), query.getUpdateDateRange()))
            .filter(note -> matchesDeleted(note.getDeleted(), query.isIncludeDeleted()))
            .collect(Collectors.toList());
    }
    
    private boolean matchesKeyword(Password password, String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return true;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return password.getTitle().toLowerCase().contains(lowerKeyword) ||
               (password.getUsername() != null && password.getUsername().toLowerCase().contains(lowerKeyword)) ||
               (password.getWebsite() != null && password.getWebsite().toLowerCase().contains(lowerKeyword)) ||
               (password.getNotes() != null && password.getNotes().toLowerCase().contains(lowerKeyword));
    }
    
    private boolean matchesKeyword(SecureNote note, String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return true;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return note.getTitle().toLowerCase().contains(lowerKeyword) ||
               (note.getTags() != null && note.getTags().toLowerCase().contains(lowerKeyword)) ||
               (note.getContentPreview() != null && note.getContentPreview().toLowerCase().contains(lowerKeyword));
    }
    
    private boolean matchesCategories(Long categoryId, List<Long> categoryIds) {
        return categoryIds == null || categoryIds.isEmpty() || categoryIds.contains(categoryId);
    }
    
    private boolean matchesWebsites(String website, List<String> websites) {
        if (websites == null || websites.isEmpty()) {
            return true;
        }
        
        if (website == null) {
            return false;
        }
        
        return websites.stream().anyMatch(w -> website.toLowerCase().contains(w.toLowerCase()));
    }
    
    private boolean matchesTotp(Password password, Boolean hasTotp) {
        if (hasTotp == null) {
            return true;
        }
        
        boolean passwordHasTotp = password.getTotpSecret() != null && !password.getTotpSecret().isEmpty();
        return hasTotp.equals(passwordHasTotp);
    }
    
    private boolean matchesStrengthRange(Password password, AdvancedSearchQuery.PasswordStrengthRange range) {
        if (range == null) {
            return true;
        }
        
        // 这里需要计算密码强度，简化实现
        int strength = 3; // 默认强度
        
        if (range.getMinStrength() != null && strength < range.getMinStrength()) {
            return false;
        }
        
        if (range.getMaxStrength() != null && strength > range.getMaxStrength()) {
            return false;
        }
        
        return true;
    }
    
    private boolean matchesNoteTypes(NoteType noteType, List<String> noteTypes) {
        return noteTypes == null || noteTypes.isEmpty() || noteTypes.contains(noteType.name());
    }
    
    private boolean matchesTags(String noteTags, List<String> queryTags) {
        if (queryTags == null || queryTags.isEmpty()) {
            return true;
        }
        
        if (noteTags == null || noteTags.isEmpty()) {
            return false;
        }
        
        String[] tags = noteTags.split(",");
        Set<String> noteTagSet = Arrays.stream(tags)
            .map(String::trim)
            .map(String::toLowerCase)
            .collect(Collectors.toSet());
        
        return queryTags.stream()
            .map(String::toLowerCase)
            .anyMatch(noteTagSet::contains);
    }
    
    private boolean matchesFavorite(Integer favorite, Boolean queryFavorite) {
        if (queryFavorite == null) {
            return true;
        }
        
        boolean isFavorite = favorite != null && favorite == 1;
        return queryFavorite.equals(isFavorite);
    }
    
    private boolean matchesPinned(Integer pinned, Boolean queryPinned) {
        if (queryPinned == null) {
            return true;
        }
        
        boolean isPinned = pinned != null && pinned == 1;
        return queryPinned.equals(isPinned);
    }
    
    private boolean matchesDateRange(LocalDateTime date, AdvancedSearchQuery.DateRange range) {
        if (range == null || date == null) {
            return true;
        }
        
        if (range.getStartDate() != null && date.isBefore(range.getStartDate())) {
            return false;
        }
        
        if (range.getEndDate() != null && date.isAfter(range.getEndDate())) {
            return false;
        }
        
        return true;
    }
    
    private boolean matchesUpdateDateRange(LocalDateTime date, AdvancedSearchQuery.DateRange range) {
        return matchesDateRange(date, range);
    }
    
    private boolean matchesDeleted(Integer deleted, boolean includeDeleted) {
        if (includeDeleted) {
            return true;
        }
        
        return deleted == null || deleted == 0;
    }
    
    private <T> List<T> applyPagination(List<T> items, AdvancedSearchQuery.PageRequest pageRequest) {
        int offset = pageRequest.getOffset();
        int size = pageRequest.getSize();
        
        if (offset >= items.size()) {
            return new ArrayList<>();
        }
        
        int endIndex = Math.min(offset + size, items.size());
        return items.subList(offset, endIndex);
    }
    
    // 其他辅助方法的简化实现
    private List<Password> applyPasswordSorting(List<Password> passwords, AdvancedSearchQuery query) {
        // 简化的排序实现
        return passwords.stream()
            .sorted(Comparator.comparing(Password::getUpdatedAt).reversed())
            .collect(Collectors.toList());
    }
    
    private List<SecureNote> applyNoteSorting(List<SecureNote> notes, AdvancedSearchQuery query) {
        // 简化的排序实现
        return notes.stream()
            .sorted(Comparator.comparing(SecureNote::getUpdatedAt).reversed())
            .collect(Collectors.toList());
    }
    
    private SearchResult.SearchStatistics buildPasswordStatistics(List<Password> passwords) {
        // 简化的统计实现
        return SearchResult.SearchStatistics.builder()
            .favoriteCount(0)
            .totpEnabledCount(passwords.stream().mapToLong(p -> p.getTotpSecret() != null ? 1 : 0).sum())
            .build();
    }
    
    private SearchResult.SearchStatistics buildNoteStatistics(List<SecureNote> notes) {
        // 简化的统计实现
        return SearchResult.SearchStatistics.builder()
            .favoriteCount(notes.stream().mapToLong(n -> n.getFavorite() == 1 ? 1 : 0).sum())
            .pinnedCount(notes.stream().mapToLong(n -> n.getPinned() == 1 ? 1 : 0).sum())
            .build();
    }
    
    private List<SearchResult.SearchSuggestion> getPasswordSearchSuggestions(String keyword, Long userId) {
        List<SearchResult.SearchSuggestion> suggestions = new ArrayList<>();

        if (keyword != null && !keyword.trim().isEmpty()) {
            // 基于现有密码生成建议
            List<Password> passwords = passwordRepository.findByUserId(userId);

            // 网站建议
            passwords.stream()
                .filter(p -> p.getWebsite() != null)
                .map(Password::getWebsite)
                .distinct()
                .filter(website -> website.toLowerCase().contains(keyword.toLowerCase()))
                .limit(3)
                .forEach(website -> suggestions.add(
                    SearchResult.SearchSuggestion.builder()
                        .type(SearchResult.SearchSuggestion.SuggestionType.WEBSITE)
                        .text(website)
                        .query(website)
                        .relevanceScore(0.8)
                        .build()
                ));
        }

        return suggestions;
    }

    private List<SearchResult.SearchSuggestion> getNoteSearchSuggestions(String keyword, Long userId) {
        List<SearchResult.SearchSuggestion> suggestions = new ArrayList<>();

        if (keyword != null && !keyword.trim().isEmpty()) {
            // 基于现有笔记生成建议
            List<SecureNote> notes = noteRepository.findByUserId(userId);

            // 标签建议
            notes.stream()
                .filter(n -> n.getTags() != null)
                .flatMap(n -> Arrays.stream(n.getTags().split(",")))
                .map(String::trim)
                .distinct()
                .filter(tag -> tag.toLowerCase().contains(keyword.toLowerCase()))
                .limit(3)
                .forEach(tag -> suggestions.add(
                    SearchResult.SearchSuggestion.builder()
                        .type(SearchResult.SearchSuggestion.SuggestionType.TAG)
                        .text(tag)
                        .query(tag)
                        .relevanceScore(0.7)
                        .build()
                ));
        }

        return suggestions;
    }
    
    private List<SearchResult.SearchSuggestion> getHistoricalSuggestions(String query, Long userId) {
        // 简化的历史建议实现
        return new ArrayList<>();
    }
    
    private List<SearchResult.SearchSuggestion> getContentSuggestions(String query, Long userId) {
        // 简化的内容建议实现
        return new ArrayList<>();
    }
    
    private List<SearchResult.SearchSuggestion> getCategorySuggestions(String query, Long userId) {
        // 简化的分类建议实现
        return new ArrayList<>();
    }
    
    private List<SearchResult.SearchSuggestion> getTagSuggestions(String query, Long userId) {
        // 简化的标签建议实现
        return new ArrayList<>();
    }
    
    private List<SearchResult.SearchSuggestion> getWebsiteSuggestions(String query, Long userId) {
        // 简化的网站建议实现
        return new ArrayList<>();
    }
    
    private void recordSearchHistory(Long userId, String keyword) {
        if (keyword != null && !keyword.trim().isEmpty()) {
            userSearchHistory.computeIfAbsent(userId, k -> new ArrayList<>()).add(keyword);
        }
    }
    
    private Long getCurrentUserId() {
        return StpUtil.getLoginIdAsLong();
    }
}
