package club.gclmit.securefox.application.service;

import club.gclmit.securefox.application.dto.BitwardenExportDTO;
import club.gclmit.securefox.application.dto.PasswordDTO;
import club.gclmit.securefox.domain.audit.AuditService;
import club.gclmit.securefox.domain.security.EncryptionService;
import club.gclmit.securefox.domain.vault.*;
import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导入导出服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
@Transactional
public class ImportExportService {
    
    private final PasswordRepository passwordRepository;
    private final PasswordCategoryRepository categoryRepository;
    private final VaultDomainService vaultDomainService;
    private final EncryptionService encryptionService;
    private final AuditService auditService;
    private final ObjectMapper objectMapper;
    
    public ImportExportService(PasswordRepository passwordRepository,
                              PasswordCategoryRepository categoryRepository,
                              VaultDomainService vaultDomainService,
                              EncryptionService encryptionService,
                              AuditService auditService,
                              ObjectMapper objectMapper) {
        this.passwordRepository = passwordRepository;
        this.categoryRepository = categoryRepository;
        this.vaultDomainService = vaultDomainService;
        this.encryptionService = encryptionService;
        this.auditService = auditService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 导入Bitwarden数据
     * 
     * @param file Bitwarden导出的JSON文件
     * @return 导入结果
     */
    public ImportResult importFromBitwarden(MultipartFile file) throws IOException {
        Long userId = getCurrentUserId();
        
        // 解析JSON文件
        String jsonContent = new String(file.getBytes());
        BitwardenExportDTO bitwardenData = objectMapper.readValue(jsonContent, BitwardenExportDTO.class);
        
        // 检查是否为加密数据
        if (Boolean.TRUE.equals(bitwardenData.getEncrypted())) {
            throw new IllegalArgumentException("不支持导入加密的Bitwarden数据，请导出未加密的JSON格式");
        }
        
        // 创建分类映射
        Map<String, Long> folderMapping = createFolderMapping(userId, bitwardenData.getFolders());
        
        // 导入密码项
        List<Password> importedPasswords = new ArrayList<>();
        int skippedCount = 0;
        
        if (bitwardenData.getItems() != null) {
            for (BitwardenExportDTO.BitwardenItem item : bitwardenData.getItems()) {
                try {
                    Password password = convertBitwardenItem(userId, item, folderMapping);
                    if (password != null) {
                        passwordRepository.save(password);
                        importedPasswords.add(password);
                        
                        // 记录审计日志
                        auditService.logPasswordCreated(password);
                    } else {
                        skippedCount++;
                    }
                } catch (Exception e) {
                    skippedCount++;
                    // 记录错误但继续处理其他项目
                    System.err.println("导入项目失败: " + item.getName() + ", 错误: " + e.getMessage());
                }
            }
        }
        
        return new ImportResult(importedPasswords.size(), skippedCount, folderMapping.size());
    }
    
    /**
     * 导出为Bitwarden格式
     * 
     * @return Bitwarden格式的JSON数据
     */
    @Transactional(readOnly = true)
    public String exportToBitwarden() throws IOException {
        Long userId = getCurrentUserId();
        
        // 获取用户的所有密码和分类
        List<Password> passwords = passwordRepository.findByUserId(userId);
        List<PasswordCategory> categories = categoryRepository.findByUserId(userId);
        
        // 构建Bitwarden数据结构
        BitwardenExportDTO bitwardenData = BitwardenExportDTO.builder()
                .encrypted(false)
                .folders(convertCategoriesToFolders(categories))
                .items(convertPasswordsToItems(passwords, categories))
                .build();
        
        return objectMapper.writeValueAsString(bitwardenData);
    }
    
    /**
     * 创建分类映射
     */
    private Map<String, Long> createFolderMapping(Long userId, List<BitwardenExportDTO.BitwardenFolder> folders) {
        Map<String, Long> folderMapping = new HashMap<>();
        
        if (folders != null) {
            for (BitwardenExportDTO.BitwardenFolder folder : folders) {
                // 检查分类是否已存在
                PasswordCategory existingCategory = categoryRepository.findByUserIdAndName(userId, folder.getName())
                        .orElse(null);
                
                if (existingCategory != null) {
                    folderMapping.put(folder.getId(), existingCategory.getId());
                } else {
                    // 创建新分类
                    PasswordCategory newCategory = PasswordCategory.createDefault(userId, folder.getName());
                    categoryRepository.save(newCategory);
                    folderMapping.put(folder.getId(), newCategory.getId());
                }
            }
        }
        
        return folderMapping;
    }
    
    /**
     * 转换Bitwarden项目为SecureFox密码
     */
    private Password convertBitwardenItem(Long userId, BitwardenExportDTO.BitwardenItem item, 
                                        Map<String, Long> folderMapping) {
        // 只处理登录类型的项目 (type = 1)
        if (item.getType() == null || item.getType() != 1 || item.getLogin() == null) {
            return null;
        }
        
        BitwardenExportDTO.BitwardenLogin login = item.getLogin();
        
        // 获取网站URL
        String website = null;
        if (login.getUris() != null && !login.getUris().isEmpty()) {
            website = login.getUris().get(0).getUri();
        }
        
        // 获取分类ID
        Long categoryId = null;
        if (item.getFolderId() != null) {
            categoryId = folderMapping.get(item.getFolderId());
        }
        
        // 创建密码实体
        Password password = Password.create(
            userId,
            item.getName(),
            login.getUsername(),
            login.getPassword(),
            website,
            categoryId,
            encryptionService
        );
        
        // 设置备注
        if (item.getNotes() != null) {
            password.setNotes(item.getNotes());
        }
        
        // 处理TOTP
        if (login.getTotp() != null && !login.getTotp().trim().isEmpty()) {
            try {
                TotpConfig totpConfig = TotpConfig.builder()
                        .secret(login.getTotp())
                        .issuer(item.getName())
                        .accountName(login.getUsername())
                        .digits(6)
                        .period(30)
                        .algorithm("SHA1")
                        .build();
                
                password.enableTotp(totpConfig, encryptionService);
            } catch (Exception e) {
                // TOTP设置失败，记录但不影响密码导入
                System.err.println("TOTP设置失败: " + item.getName() + ", 错误: " + e.getMessage());
            }
        }
        
        return password;
    }
    
    /**
     * 转换分类为Bitwarden文件夹
     */
    private List<BitwardenExportDTO.BitwardenFolder> convertCategoriesToFolders(List<PasswordCategory> categories) {
        return categories.stream()
                .map(category -> BitwardenExportDTO.BitwardenFolder.builder()
                        .id(category.getId().toString())
                        .name(category.getName())
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * 转换密码为Bitwarden项目
     */
    private List<BitwardenExportDTO.BitwardenItem> convertPasswordsToItems(List<Password> passwords, 
                                                                          List<PasswordCategory> categories) {
        Map<Long, String> categoryMap = categories.stream()
                .collect(Collectors.toMap(PasswordCategory::getId, PasswordCategory::getName));
        
        return passwords.stream()
                .map(password -> {
                    // 构建URI列表
                    List<BitwardenExportDTO.BitwardenUri> uris = new ArrayList<>();
                    if (password.getWebsite() != null) {
                        uris.add(BitwardenExportDTO.BitwardenUri.builder()
                                .match(null)
                                .uri(password.getWebsite())
                                .build());
                    }
                    
                    // 构建登录信息
                    BitwardenExportDTO.BitwardenLogin login = BitwardenExportDTO.BitwardenLogin.builder()
                            .username(password.getUsername())
                            .password(password.decryptPassword(encryptionService))
                            .totp(password.hasTotpEnabled() ? 
                                  password.getTotpConfig(encryptionService).getSecret() : null)
                            .uris(uris)
                            .build();
                    
                    return BitwardenExportDTO.BitwardenItem.builder()
                            .id(password.getId().toString())
                            .organizationId(null)
                            .folderId(password.getCategoryId() != null ? 
                                     password.getCategoryId().toString() : null)
                            .type(1) // 登录类型
                            .reprompt(0)
                            .name(password.getTitle())
                            .notes(password.getNotes())
                            .favorite(false)
                            .login(login)
                            .fields(null)
                            .creationDate(password.getCreatedAt() != null ? 
                                         password.getCreatedAt().toString() : null)
                            .revisionDate(password.getUpdatedAt() != null ? 
                                         password.getUpdatedAt().toString() : null)
                            .build();
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        return StpUtil.getLoginIdAsLong();
    }
    
    /**
     * 导入结果
     */
    public static class ImportResult {
        private final int importedCount;
        private final int skippedCount;
        private final int categoriesCount;
        
        public ImportResult(int importedCount, int skippedCount, int categoriesCount) {
            this.importedCount = importedCount;
            this.skippedCount = skippedCount;
            this.categoriesCount = categoriesCount;
        }
        
        public int getImportedCount() { return importedCount; }
        public int getSkippedCount() { return skippedCount; }
        public int getCategoriesCount() { return categoriesCount; }
        public int getTotalProcessed() { return importedCount + skippedCount; }
    }
}
