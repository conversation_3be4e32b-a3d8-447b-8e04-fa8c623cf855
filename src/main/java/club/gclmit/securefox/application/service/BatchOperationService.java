package club.gclmit.securefox.application.service;

import club.gclmit.securefox.application.command.BatchOperationCommand;
import club.gclmit.securefox.application.dto.BatchOperationResult;
import club.gclmit.securefox.domain.audit.AuditService;
import club.gclmit.securefox.domain.note.SecureNote;
import club.gclmit.securefox.domain.note.SecureNoteRepository;
import club.gclmit.securefox.domain.vault.Password;
import club.gclmit.securefox.domain.vault.PasswordRepository;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 批量操作服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchOperationService {
    
    private final PasswordRepository passwordRepository;
    private final SecureNoteRepository noteRepository;
    private final AuditService auditService;
    
    // 存储批量操作结果的缓存
    private final Map<String, BatchOperationResult> operationResults = new ConcurrentHashMap<>();
    
    /**
     * 执行批量操作
     * 
     * @param command 批量操作命令
     * @return 操作结果
     */
    public BatchOperationResult executeBatchOperation(BatchOperationCommand command) {
        String operationId = UUID.randomUUID().toString();
        Long userId = getCurrentUserId();
        
        BatchOperationResult result = BatchOperationResult.builder()
            .operationId(operationId)
            .totalItems(command.getResourceIds().size())
            .status(BatchOperationResult.BatchOperationStatus.PENDING)
            .build();
        
        operationResults.put(operationId, result);
        
        if (command.isAsync()) {
            // 异步执行
            executeBatchOperationAsync(command, result, userId);
        } else {
            // 同步执行
            executeBatchOperationSync(command, result, userId);
        }
        
        return result;
    }
    
    /**
     * 获取批量操作结果
     * 
     * @param operationId 操作ID
     * @return 操作结果
     */
    public BatchOperationResult getBatchOperationResult(String operationId) {
        return operationResults.get(operationId);
    }
    
    /**
     * 清理已完成的操作结果
     * 
     * @param operationId 操作ID
     */
    public void cleanupOperationResult(String operationId) {
        operationResults.remove(operationId);
    }
    
    /**
     * 异步执行批量操作
     */
    @Async("batchOperationExecutor")
    public CompletableFuture<Void> executeBatchOperationAsync(BatchOperationCommand command, 
                                                             BatchOperationResult result, 
                                                             Long userId) {
        try {
            executeBatchOperationSync(command, result, userId);
        } catch (Exception e) {
            log.error("批量操作异步执行失败", e);
            result.setFailed("批量操作执行失败: " + e.getMessage());
        }
        return CompletableFuture.completedFuture(null);
    }
    
    /**
     * 同步执行批量操作
     */
    @Transactional
    public void executeBatchOperationSync(BatchOperationCommand command, 
                                        BatchOperationResult result, 
                                        Long userId) {
        result.setStatus(BatchOperationResult.BatchOperationStatus.RUNNING);
        
        try {
            switch (command.getResourceType()) {
                case PASSWORD -> executePasswordBatchOperation(command, result, userId);
                case NOTE -> executeNoteBatchOperation(command, result, userId);
                case CATEGORY -> executeCategoryBatchOperation(command, result, userId);
                default -> throw new IllegalArgumentException("不支持的资源类型: " + command.getResourceType());
            }
            
            // 记录审计日志
            auditService.logBatchOperation(userId, command.getOperation().name(), 
                command.getResourceType().name(), result);
            
        } catch (Exception e) {
            log.error("批量操作执行失败", e);
            result.setFailed("批量操作执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行密码批量操作
     */
    private void executePasswordBatchOperation(BatchOperationCommand command, 
                                             BatchOperationResult result, 
                                             Long userId) {
        for (Long passwordId : command.getResourceIds()) {
            try {
                Password password = passwordRepository.findByIdAndUserId(passwordId, userId)
                    .orElse(null);
                
                if (password == null) {
                    result.addError(passwordId, "密码不存在或无权限访问");
                    continue;
                }
                
                switch (command.getOperation()) {
                    case DELETE -> {
                        passwordRepository.deleteById(password.getId());
                        result.incrementSuccessCount();
                    }
                    case UPDATE_CATEGORY -> {
                        Long categoryId = (Long) command.getParameters().get("categoryId");
                        password.setCategoryId(categoryId);
                        passwordRepository.save(password);
                        result.incrementSuccessCount();
                    }
                    case DUPLICATE_CHECK -> {
                        // 重复检查逻辑
                        boolean isDuplicate = checkPasswordDuplicate(password, userId);
                        result.getResultData().put("duplicate_" + passwordId, isDuplicate);
                        result.incrementSuccessCount();
                    }
                    case STRENGTH_CHECK -> {
                        // 强度检查逻辑
                        int strength = checkPasswordStrength(password);
                        result.getResultData().put("strength_" + passwordId, strength);
                        result.incrementSuccessCount();
                    }
                    case ARCHIVE -> {
                        password.setDeleted(1);
                        passwordRepository.save(password);
                        result.incrementSuccessCount();
                    }
                    case RESTORE -> {
                        password.setDeleted(0);
                        passwordRepository.save(password);
                        result.incrementSuccessCount();
                    }
                    default -> result.addError(passwordId, "不支持的密码操作: " + command.getOperation());
                }
                
            } catch (Exception e) {
                log.error("处理密码 {} 时发生错误", passwordId, e);
                result.addError(passwordId, "处理失败: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 执行笔记批量操作
     */
    private void executeNoteBatchOperation(BatchOperationCommand command, 
                                         BatchOperationResult result, 
                                         Long userId) {
        for (Long noteId : command.getResourceIds()) {
            try {
                SecureNote note = noteRepository.findByIdAndUserId(noteId, userId)
                    .orElse(null);
                
                if (note == null) {
                    result.addError(noteId, "笔记不存在或无权限访问");
                    continue;
                }
                
                switch (command.getOperation()) {
                    case DELETE -> {
                        noteRepository.deleteById(note.getId());
                        result.incrementSuccessCount();
                    }
                    case UPDATE_TAGS -> {
                        String tags = (String) command.getParameters().get("tags");
                        note.setTags(tags);
                        noteRepository.save(note);
                        result.incrementSuccessCount();
                    }
                    case SET_FAVORITE -> {
                        Boolean favorite = (Boolean) command.getParameters().get("favorite");
                        note.setFavorite(favorite != null ? favorite : false);
                        noteRepository.save(note);
                        result.incrementSuccessCount();
                    }
                    case SET_PINNED -> {
                        Boolean pinned = (Boolean) command.getParameters().get("pinned");
                        note.setPinned(pinned != null ? pinned : false);
                        noteRepository.save(note);
                        result.incrementSuccessCount();
                    }
                    case ARCHIVE -> {
                        note.setDeleted(1);
                        noteRepository.save(note);
                        result.incrementSuccessCount();
                    }
                    case RESTORE -> {
                        note.setDeleted(0);
                        noteRepository.save(note);
                        result.incrementSuccessCount();
                    }
                    default -> result.addError(noteId, "不支持的笔记操作: " + command.getOperation());
                }
                
            } catch (Exception e) {
                log.error("处理笔记 {} 时发生错误", noteId, e);
                result.addError(noteId, "处理失败: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 执行分类批量操作
     */
    private void executeCategoryBatchOperation(BatchOperationCommand command, 
                                             BatchOperationResult result, 
                                             Long userId) {
        // 分类批量操作的实现
        result.addError(0L, "分类批量操作暂未实现");
    }
    
    // 辅助方法
    private boolean checkPasswordDuplicate(Password password, Long userId) {
        // 简化的重复检查逻辑 - 检查相同标题的密码
        List<Password> allPasswords = passwordRepository.findByUserId(userId);
        long duplicateCount = allPasswords.stream()
            .filter(p -> p.getTitle().equals(password.getTitle()) && !p.getId().equals(password.getId()))
            .count();
        return duplicateCount > 0;
    }
    
    private int checkPasswordStrength(Password password) {
        // 这里应该调用密码强度检查服务
        // 简化实现，返回固定值
        return 3;
    }
    
    private Long getCurrentUserId() {
        return StpUtil.getLoginIdAsLong();
    }
}
