package club.gclmit.securefox.application.service;

import club.gclmit.securefox.application.command.CreateSecureNoteCommand;
import club.gclmit.securefox.application.dto.SecureNoteDTO;
import club.gclmit.securefox.domain.audit.AuditService;
import club.gclmit.securefox.domain.note.*;
import club.gclmit.securefox.domain.security.EncryptionService;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 安全笔记应用服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
@Transactional
public class SecureNoteApplicationService {
    
    private final SecureNoteRepository noteRepository;
    private final SecureNoteDomainService noteDomainService;
    private final EncryptionService encryptionService;
    private final AuditService auditService;
    
    public SecureNoteApplicationService(SecureNoteRepository noteRepository,
                                       SecureNoteDomainService noteDomainService,
                                       EncryptionService encryptionService,
                                       AuditService auditService) {
        this.noteRepository = noteRepository;
        this.noteDomainService = noteDomainService;
        this.encryptionService = encryptionService;
        this.auditService = auditService;
    }
    
    /**
     * 创建安全笔记
     * 
     * @param command 创建命令
     * @return 安全笔记DTO
     */
    public SecureNoteDTO createNote(CreateSecureNoteCommand command) {
        Long userId = getCurrentUserId();
        
        SecureNote note = noteDomainService.createNote(
            userId,
            command.getTitle(),
            command.getContent(),
            command.getNoteType(),
            command.getCategoryId()
        );
        
        // 设置收藏和置顶状态
        if (Boolean.TRUE.equals(command.getFavorite())) {
            note.setFavorite(true);
        }
        if (Boolean.TRUE.equals(command.getPinned())) {
            note.setPinned(true);
        }
        
        // 设置标签
        if (command.getTags() != null) {
            note.setTags(command.getTags());
        }
        
        noteRepository.save(note);
        
        // 记录审计日志
        auditService.log(userId, "CREATE_NOTE", "SECURE_NOTE", note.getId(),
                        "创建安全笔记: " + note.getTitle(), null, null);
        
        return SecureNoteDTO.from(note);
    }
    
    /**
     * 获取用户所有安全笔记
     * 
     * @return 安全笔记列表
     */
    @Transactional(readOnly = true)
    public List<SecureNoteDTO> getUserNotes() {
        Long userId = getCurrentUserId();
        List<SecureNote> notes = noteRepository.findByUserId(userId);
        
        return notes.stream()
                .map(note -> {
                    String preview = noteDomainService.getContentPreview(note, 100);
                    int length = note.getContentLength(encryptionService);
                    return SecureNoteDTO.fromWithPreview(note, preview, length);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 根据类型获取安全笔记
     * 
     * @param noteType 笔记类型
     * @return 安全笔记列表
     */
    @Transactional(readOnly = true)
    public List<SecureNoteDTO> getNotesByType(NoteType noteType) {
        Long userId = getCurrentUserId();
        List<SecureNote> notes = noteRepository.findByUserIdAndNoteType(userId, noteType);
        
        return notes.stream()
                .map(note -> {
                    String preview = noteDomainService.getContentPreview(note, 100);
                    int length = note.getContentLength(encryptionService);
                    return SecureNoteDTO.fromWithPreview(note, preview, length);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 根据ID获取安全笔记
     * 
     * @param noteId 笔记ID
     * @return 安全笔记DTO
     */
    @Transactional(readOnly = true)
    public SecureNoteDTO getNote(Long noteId) {
        Long userId = getCurrentUserId();
        SecureNote note = noteRepository.findByIdAndUserId(noteId, userId)
                .orElseThrow(() -> new IllegalArgumentException("安全笔记不存在"));
        
        String preview = noteDomainService.getContentPreview(note, 200);
        int length = note.getContentLength(encryptionService);
        return SecureNoteDTO.fromWithPreview(note, preview, length);
    }
    
    /**
     * 获取安全笔记完整内容
     * 
     * @param noteId 笔记ID
     * @return 明文内容
     */
    @Transactional(readOnly = true)
    public String getNoteContent(Long noteId) {
        Long userId = getCurrentUserId();
        SecureNote note = noteRepository.findByIdAndUserId(noteId, userId)
                .orElseThrow(() -> new IllegalArgumentException("安全笔记不存在"));
        
        // 记录审计日志
        auditService.log(userId, "VIEW_NOTE", "SECURE_NOTE", noteId,
                        "查看安全笔记内容: " + note.getTitle(), null, null);
        
        return note.decryptContent(encryptionService);
    }
    
    /**
     * 更新安全笔记
     * 
     * @param noteId 笔记ID
     * @param command 更新命令
     * @return 安全笔记DTO
     */
    public SecureNoteDTO updateNote(Long noteId, CreateSecureNoteCommand command) {
        Long userId = getCurrentUserId();
        
        boolean success = noteDomainService.updateNote(
            noteId,
            userId,
            command.getTitle(),
            command.getContent(),
            command.getNoteType(),
            command.getCategoryId()
        );
        
        if (!success) {
            throw new IllegalArgumentException("安全笔记更新失败");
        }
        
        SecureNote note = noteRepository.findByIdAndUserId(noteId, userId)
                .orElseThrow(() -> new IllegalArgumentException("安全笔记不存在"));
        
        // 更新其他属性
        if (command.getFavorite() != null) {
            note.setFavorite(command.getFavorite());
        }
        if (command.getPinned() != null) {
            note.setPinned(command.getPinned());
        }
        if (command.getTags() != null) {
            note.setTags(command.getTags());
        }
        
        noteRepository.save(note);
        
        // 记录审计日志
        auditService.log(userId, "UPDATE_NOTE", "SECURE_NOTE", noteId,
                        "更新安全笔记: " + note.getTitle(), null, null);
        
        return SecureNoteDTO.from(note);
    }
    
    /**
     * 删除安全笔记
     * 
     * @param noteId 笔记ID
     */
    public void deleteNote(Long noteId) {
        Long userId = getCurrentUserId();
        SecureNote note = noteRepository.findByIdAndUserId(noteId, userId)
                .orElseThrow(() -> new IllegalArgumentException("安全笔记不存在"));
        
        noteRepository.deleteById(noteId);
        
        // 记录审计日志
        auditService.log(userId, "DELETE_NOTE", "SECURE_NOTE", noteId,
                        "删除安全笔记: " + note.getTitle(), null, null);
    }
    
    /**
     * 搜索安全笔记
     * 
     * @param keyword 关键词
     * @return 安全笔记列表
     */
    @Transactional(readOnly = true)
    public List<SecureNoteDTO> searchNotes(String keyword) {
        Long userId = getCurrentUserId();
        List<SecureNote> notes = noteDomainService.searchNotes(userId, keyword);
        
        return notes.stream()
                .map(note -> {
                    String preview = noteDomainService.getContentPreview(note, 100);
                    int length = note.getContentLength(encryptionService);
                    return SecureNoteDTO.fromWithPreview(note, preview, length);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 设置收藏状态
     * 
     * @param noteId 笔记ID
     * @param favorite 是否收藏
     * @return 是否成功
     */
    public boolean setFavorite(Long noteId, boolean favorite) {
        Long userId = getCurrentUserId();
        return noteDomainService.setFavorite(noteId, userId, favorite);
    }
    
    /**
     * 设置置顶状态
     * 
     * @param noteId 笔记ID
     * @param pinned 是否置顶
     * @return 是否成功
     */
    public boolean setPinned(Long noteId, boolean pinned) {
        Long userId = getCurrentUserId();
        return noteDomainService.setPinned(noteId, userId, pinned);
    }
    
    /**
     * 获取收藏的安全笔记
     * 
     * @return 安全笔记列表
     */
    @Transactional(readOnly = true)
    public List<SecureNoteDTO> getFavoriteNotes() {
        Long userId = getCurrentUserId();
        List<SecureNote> notes = noteRepository.findByUserIdAndFavorite(userId);
        
        return notes.stream()
                .map(SecureNoteDTO::from)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取置顶的安全笔记
     * 
     * @return 安全笔记列表
     */
    @Transactional(readOnly = true)
    public List<SecureNoteDTO> getPinnedNotes() {
        Long userId = getCurrentUserId();
        List<SecureNote> notes = noteRepository.findByUserIdAndPinned(userId);
        
        return notes.stream()
                .map(SecureNoteDTO::from)
                .collect(Collectors.toList());
    }
    
    /**
     * 生成安全笔记统计
     * 
     * @return 统计信息
     */
    @Transactional(readOnly = true)
    public NoteStatistics generateStatistics() {
        Long userId = getCurrentUserId();
        SecureNoteDomainService.NoteStatistics domainStats = noteDomainService.generateStatistics(userId);
        
        return new NoteStatistics(
            domainStats.getTotalNotes(),
            domainStats.getFavoriteNotes(),
            domainStats.getPinnedNotes(),
            domainStats.getNotesByType(),
            domainStats.getRecentNotes().stream()
                    .map(SecureNoteDTO::from)
                    .collect(Collectors.toList()),
            domainStats.getTotalCharacters()
        );
    }
    
    /**
     * 检查内容安全性
     * 
     * @param content 内容
     * @return 安全检查结果
     */
    @Transactional(readOnly = true)
    public ContentSecurityCheck checkContentSecurity(String content) {
        SecureNoteDomainService.ContentSecurityCheck domainCheck = 
            noteDomainService.checkContentSecurity(content);
        
        return new ContentSecurityCheck(domainCheck.isSafe(), domainCheck.getMessage());
    }
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        return StpUtil.getLoginIdAsLong();
    }
    
    /**
     * 笔记统计信息
     */
    public static class NoteStatistics {
        private final long totalNotes;
        private final long favoriteNotes;
        private final long pinnedNotes;
        private final java.util.Map<NoteType, Long> notesByType;
        private final List<SecureNoteDTO> recentNotes;
        private final long totalCharacters;
        
        public NoteStatistics(long totalNotes, long favoriteNotes, long pinnedNotes,
                            java.util.Map<NoteType, Long> notesByType, List<SecureNoteDTO> recentNotes,
                            long totalCharacters) {
            this.totalNotes = totalNotes;
            this.favoriteNotes = favoriteNotes;
            this.pinnedNotes = pinnedNotes;
            this.notesByType = notesByType;
            this.recentNotes = recentNotes;
            this.totalCharacters = totalCharacters;
        }
        
        public long getTotalNotes() { return totalNotes; }
        public long getFavoriteNotes() { return favoriteNotes; }
        public long getPinnedNotes() { return pinnedNotes; }
        public java.util.Map<NoteType, Long> getNotesByType() { return notesByType; }
        public List<SecureNoteDTO> getRecentNotes() { return recentNotes; }
        public long getTotalCharacters() { return totalCharacters; }
    }
    
    /**
     * 内容安全检查结果
     */
    public static class ContentSecurityCheck {
        private final boolean isSafe;
        private final String message;
        
        public ContentSecurityCheck(boolean isSafe, String message) {
            this.isSafe = isSafe;
            this.message = message;
        }
        
        public boolean isSafe() { return isSafe; }
        public String getMessage() { return message; }
    }
}
