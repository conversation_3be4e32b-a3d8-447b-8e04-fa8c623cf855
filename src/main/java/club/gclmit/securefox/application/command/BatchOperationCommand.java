package club.gclmit.securefox.application.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 批量操作命令
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperationCommand {
    
    /**
     * 操作类型
     */
    @NotNull(message = "操作类型不能为空")
    private BatchOperationType operation;
    
    /**
     * 目标资源类型
     */
    @NotNull(message = "资源类型不能为空")
    private ResourceType resourceType;
    
    /**
     * 目标资源ID列表
     */
    @NotEmpty(message = "资源ID列表不能为空")
    private List<Long> resourceIds;
    
    /**
     * 操作参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 是否异步执行
     */
    @Builder.Default
    private boolean async = true;
    
    /**
     * 批量操作类型
     */
    public enum BatchOperationType {
        DELETE("删除"),
        UPDATE_CATEGORY("更新分类"),
        UPDATE_TAGS("更新标签"),
        SET_FAVORITE("设置收藏"),
        SET_PINNED("设置置顶"),
        EXPORT("导出"),
        DUPLICATE_CHECK("重复检查"),
        STRENGTH_CHECK("强度检查"),
        BREACH_CHECK("泄露检查"),
        MOVE_TO_CATEGORY("移动到分类"),
        COPY("复制"),
        ARCHIVE("归档"),
        RESTORE("恢复");
        
        private final String description;
        
        BatchOperationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 资源类型
     */
    public enum ResourceType {
        PASSWORD("密码"),
        NOTE("笔记"),
        CATEGORY("分类");
        
        private final String description;
        
        ResourceType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
