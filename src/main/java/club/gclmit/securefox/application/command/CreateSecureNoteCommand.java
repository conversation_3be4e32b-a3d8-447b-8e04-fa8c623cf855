package club.gclmit.securefox.application.command;

import club.gclmit.securefox.domain.note.NoteType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 创建安全笔记命令
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSecureNoteCommand {
    
    /**
     * 笔记标题
     */
    @NotBlank(message = "笔记标题不能为空")
    @Size(max = 200, message = "笔记标题长度不能超过200个字符")
    private String title;
    
    /**
     * 笔记内容
     */
    @NotBlank(message = "笔记内容不能为空")
    @Size(max = 100000, message = "笔记内容长度不能超过100000个字符")
    private String content;
    
    /**
     * 笔记类型
     */
    @NotNull(message = "笔记类型不能为空")
    private NoteType noteType;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 是否收藏
     */
    private Boolean favorite = false;
    
    /**
     * 是否置顶
     */
    private Boolean pinned = false;
}
