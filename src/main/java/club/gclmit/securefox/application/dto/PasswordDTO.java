package club.gclmit.securefox.application.dto;

import club.gclmit.securefox.domain.vault.Password;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 密码DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PasswordDTO {
    
    /**
     * 密码ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 网站地址
     */
    private String website;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 是否启用TOTP
     */
    private Boolean hasTotpEnabled;
    
    /**
     * TOTP发行者
     */
    private String totpIssuer;
    
    /**
     * TOTP账户名
     */
    private String totpAccountName;
    
    /**
     * 同步版本号
     */
    private Integer syncVersion;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 从领域对象转换为DTO
     * 
     * @param password 密码领域对象
     * @return 密码DTO
     */
    public static PasswordDTO from(Password password) {
        if (password == null) {
            return null;
        }
        
        return PasswordDTO.builder()
                .id(password.getId())
                .userId(password.getUserId())
                .title(password.getTitle())
                .username(password.getUsername())
                .website(password.getWebsite())
                .notes(password.getNotes())
                .categoryId(password.getCategoryId())
                .hasTotpEnabled(password.hasTotpEnabled())
                .totpIssuer(password.getTotpIssuer())
                .totpAccountName(password.getTotpAccountName())
                .syncVersion(password.getSyncVersion())
                .createdAt(password.getCreatedAt())
                .updatedAt(password.getUpdatedAt())
                .build();
    }
}
