package club.gclmit.securefox.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 高级搜索查询条件
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdvancedSearchQuery {
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 搜索类型
     */
    @Builder.Default
    private SearchType searchType = SearchType.ALL;
    
    /**
     * 分类ID列表
     */
    private List<Long> categoryIds;
    
    /**
     * 网站域名列表（仅密码搜索）
     */
    private List<String> websites;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 笔记类型列表（仅笔记搜索）
     */
    private List<String> noteTypes;
    
    /**
     * 是否启用TOTP（仅密码搜索）
     */
    private Boolean hasTotp;
    
    /**
     * 密码强度范围（仅密码搜索）
     */
    private PasswordStrengthRange strengthRange;
    
    /**
     * 收藏状态（仅笔记搜索）
     */
    private Boolean favorite;
    
    /**
     * 置顶状态（仅笔记搜索）
     */
    private Boolean pinned;
    
    /**
     * 创建时间范围
     */
    private DateRange dateRange;
    
    /**
     * 更新时间范围
     */
    private DateRange updateDateRange;
    
    /**
     * 排序字段
     */
    @Builder.Default
    private SortField sortField = SortField.UPDATED_AT;
    
    /**
     * 排序方向
     */
    @Builder.Default
    private SortDirection sortDirection = SortDirection.DESC;
    
    /**
     * 分页信息
     */
    @Builder.Default
    private PageRequest pageRequest = new PageRequest(1, 20);
    
    /**
     * 是否包含已删除项目
     */
    @Builder.Default
    private boolean includeDeleted = false;
    
    /**
     * 搜索类型
     */
    public enum SearchType {
        ALL("全部"),
        PASSWORD("密码"),
        NOTE("笔记"),
        CATEGORY("分类");
        
        private final String description;
        
        SearchType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 密码强度范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordStrengthRange {
        private Integer minStrength; // 0-5
        private Integer maxStrength; // 0-5
        
        public static PasswordStrengthRange of(int min, int max) {
            return PasswordStrengthRange.builder()
                .minStrength(min)
                .maxStrength(max)
                .build();
        }
        
        public static PasswordStrengthRange weak() {
            return of(0, 2);
        }
        
        public static PasswordStrengthRange medium() {
            return of(3, 3);
        }
        
        public static PasswordStrengthRange strong() {
            return of(4, 5);
        }
    }
    
    /**
     * 日期范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRange {
        private LocalDateTime startDate;
        private LocalDateTime endDate;
        
        public static DateRange of(LocalDateTime start, LocalDateTime end) {
            return DateRange.builder()
                .startDate(start)
                .endDate(end)
                .build();
        }
        
        public static DateRange lastWeek() {
            LocalDateTime now = LocalDateTime.now();
            return of(now.minusWeeks(1), now);
        }
        
        public static DateRange lastMonth() {
            LocalDateTime now = LocalDateTime.now();
            return of(now.minusMonths(1), now);
        }
        
        public static DateRange lastYear() {
            LocalDateTime now = LocalDateTime.now();
            return of(now.minusYears(1), now);
        }
    }
    
    /**
     * 排序字段
     */
    public enum SortField {
        TITLE("标题"),
        CREATED_AT("创建时间"),
        UPDATED_AT("更新时间"),
        CATEGORY("分类"),
        STRENGTH("密码强度"),
        NOTE_TYPE("笔记类型"),
        FAVORITE("收藏状态"),
        PINNED("置顶状态");
        
        private final String description;
        
        SortField(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 排序方向
     */
    public enum SortDirection {
        ASC("升序"),
        DESC("降序");
        
        private final String description;
        
        SortDirection(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 分页请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PageRequest {
        @Builder.Default
        private int page = 1; // 页码，从1开始
        
        @Builder.Default
        private int size = 20; // 每页大小
        
        public PageRequest(int page, int size) {
            this.page = Math.max(1, page);
            this.size = Math.max(1, Math.min(100, size)); // 限制每页最大100条
        }
        
        public int getOffset() {
            return (page - 1) * size;
        }
    }
}
