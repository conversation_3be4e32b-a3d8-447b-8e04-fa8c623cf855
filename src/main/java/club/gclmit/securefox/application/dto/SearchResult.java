package club.gclmit.securefox.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 搜索结果
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchResult<T> {
    
    /**
     * 搜索结果列表
     */
    private List<T> items;
    
    /**
     * 总记录数
     */
    private long totalCount;
    
    /**
     * 当前页码
     */
    private int currentPage;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 搜索耗时（毫秒）
     */
    private long searchTimeMs;
    
    /**
     * 搜索统计信息
     */
    private SearchStatistics statistics;
    
    /**
     * 搜索建议
     */
    private List<SearchSuggestion> suggestions;
    
    /**
     * 高亮信息
     */
    private Map<String, String> highlights;
    
    /**
     * 创建搜索结果
     */
    public static <T> SearchResult<T> of(List<T> items, long totalCount, 
                                        AdvancedSearchQuery.PageRequest pageRequest) {
        int totalPages = (int) Math.ceil((double) totalCount / pageRequest.getSize());
        
        return SearchResult.<T>builder()
            .items(items)
            .totalCount(totalCount)
            .currentPage(pageRequest.getPage())
            .pageSize(pageRequest.getSize())
            .totalPages(totalPages)
            .hasNext(pageRequest.getPage() < totalPages)
            .hasPrevious(pageRequest.getPage() > 1)
            .build();
    }
    
    /**
     * 创建空搜索结果
     */
    public static <T> SearchResult<T> empty(AdvancedSearchQuery.PageRequest pageRequest) {
        return SearchResult.<T>builder()
            .items(List.of())
            .totalCount(0)
            .currentPage(pageRequest.getPage())
            .pageSize(pageRequest.getSize())
            .totalPages(0)
            .hasNext(false)
            .hasPrevious(false)
            .build();
    }
    
    /**
     * 搜索统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchStatistics {
        
        /**
         * 按分类统计
         */
        private Map<String, Long> categoryStats;
        
        /**
         * 按类型统计
         */
        private Map<String, Long> typeStats;
        
        /**
         * 按强度统计（仅密码）
         */
        private Map<Integer, Long> strengthStats;
        
        /**
         * 按标签统计
         */
        private Map<String, Long> tagStats;
        
        /**
         * 按时间统计
         */
        private Map<String, Long> timeStats;
        
        /**
         * 收藏数量
         */
        private long favoriteCount;
        
        /**
         * 置顶数量
         */
        private long pinnedCount;
        
        /**
         * TOTP启用数量
         */
        private long totpEnabledCount;
    }
    
    /**
     * 搜索建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchSuggestion {
        
        /**
         * 建议类型
         */
        private SuggestionType type;
        
        /**
         * 建议文本
         */
        private String text;
        
        /**
         * 建议查询
         */
        private String query;
        
        /**
         * 相关度评分
         */
        private double relevanceScore;
        
        /**
         * 匹配数量
         */
        private long matchCount;
        
        /**
         * 建议类型
         */
        public enum SuggestionType {
            KEYWORD("关键词"),
            CATEGORY("分类"),
            TAG("标签"),
            WEBSITE("网站"),
            NOTE_TYPE("笔记类型"),
            HISTORY("历史搜索");
            
            private final String description;
            
            SuggestionType(String description) {
                this.description = description;
            }
            
            public String getDescription() {
                return description;
            }
        }
    }
}
