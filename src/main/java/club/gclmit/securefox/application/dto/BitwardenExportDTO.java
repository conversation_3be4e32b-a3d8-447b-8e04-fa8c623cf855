package club.gclmit.securefox.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Bitwarden导出数据DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BitwardenExportDTO {
    
    /**
     * 加密状态
     */
    private Boolean encrypted;
    
    /**
     * 文件夹列表
     */
    private List<BitwardenFolder> folders;
    
    /**
     * 密码项列表
     */
    private List<BitwardenItem> items;
    
    /**
     * Bitwarden文件夹
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenFolder {
        private String id;
        private String name;
    }
    
    /**
     * Bitwarden密码项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenItem {
        
        /**
         * 项目ID
         */
        private String id;
        
        /**
         * 组织ID
         */
        @JsonProperty("organizationId")
        private String organizationId;
        
        /**
         * 文件夹ID
         */
        @JsonProperty("folderId")
        private String folderId;
        
        /**
         * 类型 (1=登录, 2=安全笔记, 3=卡片, 4=身份)
         */
        private Integer type;
        
        /**
         * 重新提示类型
         */
        @JsonProperty("reprompt")
        private Integer reprompt;
        
        /**
         * 名称
         */
        private String name;
        
        /**
         * 备注
         */
        private String notes;
        
        /**
         * 是否收藏
         */
        private Boolean favorite;
        
        /**
         * 登录信息
         */
        private BitwardenLogin login;
        
        /**
         * 安全笔记
         */
        @JsonProperty("secureNote")
        private BitwardenSecureNote secureNote;
        
        /**
         * 卡片信息
         */
        private BitwardenCard card;
        
        /**
         * 身份信息
         */
        private BitwardenIdentity identity;
        
        /**
         * 字段列表
         */
        private List<BitwardenField> fields;
        
        /**
         * 创建时间
         */
        @JsonProperty("creationDate")
        private String creationDate;
        
        /**
         * 修改时间
         */
        @JsonProperty("revisionDate")
        private String revisionDate;
    }
    
    /**
     * Bitwarden登录信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenLogin {
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码
         */
        private String password;
        
        /**
         * TOTP密钥
         */
        private String totp;
        
        /**
         * URI列表
         */
        private List<BitwardenUri> uris;
    }
    
    /**
     * Bitwarden URI
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenUri {
        
        /**
         * 匹配类型
         */
        private Integer match;
        
        /**
         * URI地址
         */
        private String uri;
    }
    
    /**
     * Bitwarden安全笔记
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenSecureNote {
        
        /**
         * 类型
         */
        private Integer type;
    }
    
    /**
     * Bitwarden卡片
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenCard {
        
        /**
         * 持卡人姓名
         */
        @JsonProperty("cardholderName")
        private String cardholderName;
        
        /**
         * 品牌
         */
        private String brand;
        
        /**
         * 卡号
         */
        private String number;
        
        /**
         * 过期月份
         */
        @JsonProperty("expMonth")
        private String expMonth;
        
        /**
         * 过期年份
         */
        @JsonProperty("expYear")
        private String expYear;
        
        /**
         * 安全码
         */
        private String code;
    }
    
    /**
     * Bitwarden身份
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenIdentity {
        
        /**
         * 称谓
         */
        private String title;
        
        /**
         * 名字
         */
        @JsonProperty("firstName")
        private String firstName;
        
        /**
         * 中间名
         */
        @JsonProperty("middleName")
        private String middleName;
        
        /**
         * 姓氏
         */
        @JsonProperty("lastName")
        private String lastName;
        
        /**
         * 地址1
         */
        @JsonProperty("address1")
        private String address1;
        
        /**
         * 地址2
         */
        @JsonProperty("address2")
        private String address2;
        
        /**
         * 地址3
         */
        @JsonProperty("address3")
        private String address3;
        
        /**
         * 城市
         */
        private String city;
        
        /**
         * 州/省
         */
        private String state;
        
        /**
         * 邮政编码
         */
        @JsonProperty("postalCode")
        private String postalCode;
        
        /**
         * 国家
         */
        private String country;
        
        /**
         * 公司
         */
        private String company;
        
        /**
         * 邮箱
         */
        private String email;
        
        /**
         * 电话
         */
        private String phone;
        
        /**
         * 社会安全号码
         */
        private String ssn;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 护照号码
         */
        @JsonProperty("passportNumber")
        private String passportNumber;
        
        /**
         * 驾照号码
         */
        @JsonProperty("licenseNumber")
        private String licenseNumber;
    }
    
    /**
     * Bitwarden字段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BitwardenField {
        
        /**
         * 名称
         */
        private String name;
        
        /**
         * 值
         */
        private String value;
        
        /**
         * 类型 (0=文本, 1=隐藏, 2=布尔)
         */
        private Integer type;
    }
}
