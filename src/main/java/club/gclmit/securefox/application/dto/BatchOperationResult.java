package club.gclmit.securefox.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量操作结果
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchOperationResult {
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 操作状态
     */
    @Builder.Default
    private BatchOperationStatus status = BatchOperationStatus.PENDING;
    
    /**
     * 总项目数
     */
    private int totalItems;
    
    /**
     * 成功处理数
     */
    @Builder.Default
    private int successCount = 0;
    
    /**
     * 失败处理数
     */
    @Builder.Default
    private int failureCount = 0;
    
    /**
     * 跳过处理数
     */
    @Builder.Default
    private int skippedCount = 0;
    
    /**
     * 处理进度 (0-100)
     */
    @Builder.Default
    private int progress = 0;
    
    /**
     * 开始时间
     */
    @Builder.Default
    private LocalDateTime startTime = LocalDateTime.now();
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 详细错误列表
     */
    @Builder.Default
    private List<BatchOperationError> errors = new ArrayList<>();
    
    /**
     * 操作结果数据
     */
    @Builder.Default
    private Map<String, Object> resultData = new HashMap<>();
    
    /**
     * 增加成功计数
     */
    public void incrementSuccessCount() {
        this.successCount++;
        updateProgress();
    }
    
    /**
     * 增加失败计数
     */
    public void incrementFailureCount() {
        this.failureCount++;
        updateProgress();
    }
    
    /**
     * 增加跳过计数
     */
    public void incrementSkippedCount() {
        this.skippedCount++;
        updateProgress();
    }
    
    /**
     * 添加错误
     */
    public void addError(Long resourceId, String errorMessage) {
        this.errors.add(new BatchOperationError(resourceId, errorMessage));
        incrementFailureCount();
    }
    
    /**
     * 添加错误
     */
    public void addError(Long resourceId, String errorMessage, Exception exception) {
        this.errors.add(new BatchOperationError(resourceId, errorMessage, exception.getClass().getSimpleName()));
        incrementFailureCount();
    }
    
    /**
     * 更新进度
     */
    private void updateProgress() {
        if (totalItems > 0) {
            int processedItems = successCount + failureCount + skippedCount;
            this.progress = (int) ((double) processedItems / totalItems * 100);
            
            if (processedItems >= totalItems) {
                this.status = failureCount > 0 ? BatchOperationStatus.COMPLETED_WITH_ERRORS : BatchOperationStatus.COMPLETED;
                this.endTime = LocalDateTime.now();
            }
        }
    }
    
    /**
     * 设置完成状态
     */
    public void setCompleted() {
        this.status = BatchOperationStatus.COMPLETED;
        this.progress = 100;
        this.endTime = LocalDateTime.now();
    }
    
    /**
     * 设置失败状态
     */
    public void setFailed(String errorMessage) {
        this.status = BatchOperationStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
    }
    
    /**
     * 获取处理耗时（毫秒）
     */
    public long getDurationMs() {
        if (startTime == null) return 0;
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
    
    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return status == BatchOperationStatus.COMPLETED || 
               status == BatchOperationStatus.COMPLETED_WITH_ERRORS ||
               status == BatchOperationStatus.FAILED;
    }
    
    /**
     * 批量操作状态
     */
    public enum BatchOperationStatus {
        PENDING("等待中"),
        RUNNING("执行中"),
        COMPLETED("已完成"),
        COMPLETED_WITH_ERRORS("完成但有错误"),
        FAILED("失败"),
        CANCELLED("已取消");
        
        private final String description;
        
        BatchOperationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 批量操作错误
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchOperationError {
        private Long resourceId;
        private String errorMessage;
        private String exceptionType;
        
        public BatchOperationError(Long resourceId, String errorMessage) {
            this.resourceId = resourceId;
            this.errorMessage = errorMessage;
        }
    }
}
