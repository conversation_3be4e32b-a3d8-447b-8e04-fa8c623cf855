package club.gclmit.securefox.application.dto;

import club.gclmit.securefox.domain.note.NoteType;
import club.gclmit.securefox.domain.note.SecureNote;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 安全笔记DTO
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecureNoteDTO {
    
    /**
     * 笔记ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 笔记标题
     */
    private String title;
    
    /**
     * 笔记类型
     */
    private NoteType noteType;
    
    /**
     * 笔记类型描述
     */
    private String noteTypeDescription;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 是否收藏
     */
    private Boolean favorite;
    
    /**
     * 是否置顶
     */
    private Boolean pinned;
    
    /**
     * 内容预览（不包含完整内容）
     */
    private String contentPreview;
    
    /**
     * 内容长度
     */
    private Integer contentLength;
    
    /**
     * 同步版本号
     */
    private Integer syncVersion;
    
    /**
     * 最后修改设备
     */
    private String lastModifiedDevice;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 从领域对象转换为DTO（不包含内容）
     * 
     * @param note 安全笔记领域对象
     * @return 安全笔记DTO
     */
    public static SecureNoteDTO from(SecureNote note) {
        if (note == null) {
            return null;
        }
        
        return SecureNoteDTO.builder()
                .id(note.getId())
                .userId(note.getUserId())
                .title(note.getTitle())
                .noteType(note.getNoteType())
                .noteTypeDescription(note.getNoteType().getDescription())
                .categoryId(note.getCategoryId())
                .tags(note.getTags())
                .favorite(note.getFavorite())
                .pinned(note.getPinned())
                .syncVersion(note.getSyncVersion())
                .lastModifiedDevice(note.getLastModifiedDevice())
                .createdAt(note.getCreatedAt())
                .updatedAt(note.getUpdatedAt())
                .build();
    }
    
    /**
     * 从领域对象转换为DTO（包含内容预览）
     * 
     * @param note 安全笔记领域对象
     * @param contentPreview 内容预览
     * @param contentLength 内容长度
     * @return 安全笔记DTO
     */
    public static SecureNoteDTO fromWithPreview(SecureNote note, String contentPreview, Integer contentLength) {
        SecureNoteDTO dto = from(note);
        if (dto != null) {
            dto.setContentPreview(contentPreview);
            dto.setContentLength(contentLength);
        }
        return dto;
    }
}
