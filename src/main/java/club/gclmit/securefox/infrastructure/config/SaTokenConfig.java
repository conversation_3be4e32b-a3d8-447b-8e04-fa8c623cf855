package club.gclmit.securefox.infrastructure.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * SA-Token配置类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {
    
    /**
     * 注册SA-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            SaRouter
                // 拦截所有路由
                .match("/**")
                // 排除登录注册等接口
                .notMatch("/api/v1/users/login")
                .notMatch("/api/v1/users/register")
                // 排除静态资源
                .notMatch("/favicon.ico")
                .notMatch("/error")
                .notMatch("/actuator/**")
                // 排除Swagger文档
                .notMatch("/swagger-ui/**")
                .notMatch("/v3/api-docs/**")
                .notMatch("/swagger-resources/**")
                .notMatch("/webjars/**")
                // 执行登录校验
                .check(r -> StpUtil.checkLogin());
        })).addPathPatterns("/**");
    }
}
