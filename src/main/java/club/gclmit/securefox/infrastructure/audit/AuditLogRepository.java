package club.gclmit.securefox.infrastructure.audit;

import club.gclmit.securefox.domain.audit.AuditLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志仓储接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public interface AuditLogRepository {
    
    /**
     * 保存审计日志
     * 
     * @param auditLog 审计日志
     * @return 保存后的审计日志
     */
    AuditLog save(AuditLog auditLog);
    
    /**
     * 根据用户ID和时间范围查询审计日志
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码
     * @param size 每页大小
     * @return 审计日志列表
     */
    List<AuditLog> findByUserIdAndTimeRange(Long userId, LocalDateTime startTime, 
                                           LocalDateTime endTime, int page, int size);
    
    /**
     * 根据资源类型和资源ID查询审计日志
     * 
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param page 页码
     * @param size 每页大小
     * @return 审计日志列表
     */
    List<AuditLog> findByResourceTypeAndResourceId(String resourceType, Long resourceId, 
                                                  int page, int size);
    
    /**
     * 统计用户在指定时间范围内的操作次数
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    long countByUserIdAndActionAndTimeRange(Long userId, String action, 
                                          LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的审计日志
     * 
     * @param cutoffTime 截止时间
     * @return 删除的日志数量
     */
    long deleteByCreatedAtBefore(LocalDateTime cutoffTime);
    
    /**
     * 根据用户ID查询最近的审计日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 审计日志列表
     */
    List<AuditLog> findRecentByUserId(Long userId, int limit);
    
    /**
     * 查询指定操作类型的审计日志
     * 
     * @param action 操作类型
     * @param page 页码
     * @param size 每页大小
     * @return 审计日志列表
     */
    List<AuditLog> findByAction(String action, int page, int size);
    
    /**
     * 统计总的审计日志数量
     * 
     * @return 总数量
     */
    long count();
}
