package club.gclmit.securefox.infrastructure.audit;

import club.gclmit.securefox.application.dto.BatchOperationResult;
import club.gclmit.securefox.domain.audit.AuditLog;
import club.gclmit.securefox.domain.audit.AuditService;
import club.gclmit.securefox.domain.user.User;
import club.gclmit.securefox.domain.vault.Password;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审计服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
public class AuditServiceImpl implements AuditService {
    
    private final AuditLogRepository auditLogRepository;
    private final ObjectMapper objectMapper;
    
    @Value("${securefox.audit.enabled:true}")
    private boolean auditEnabled;
    
    @Value("${securefox.audit.log-sensitive-operations:true}")
    private boolean logSensitiveOperations;
    
    public AuditServiceImpl(AuditLogRepository auditLogRepository, ObjectMapper objectMapper) {
        this.auditLogRepository = auditLogRepository;
        this.objectMapper = objectMapper;
    }
    
    @Override
    public void log(Long userId, String action, String resourceType, Long resourceId,
                   String details, String ipAddress, String userAgent) {
        if (!auditEnabled) {
            return;
        }
        
        try {
            AuditLog auditLog = AuditLog.createWithDetails(userId, action, resourceType,
                                                         resourceId, details, ipAddress, userAgent);
            auditLogRepository.save(auditLog);
        } catch (Exception e) {
            // 审计日志记录失败不应该影响业务操作
            System.err.println("审计日志记录失败: " + e.getMessage());
        }
    }
    
    @Override
    public void logUserLogin(User user, String ipAddress, String userAgent) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", user.getUsername());
        details.put("email", user.getEmail());
        details.put("loginTime", LocalDateTime.now());
        
        log(user.getId(), AuditLog.Actions.LOGIN, AuditLog.ResourceTypes.USER,
            user.getId(), toJson(details), ipAddress, userAgent);
    }
    
    @Override
    public void logUserRegister(User user, String ipAddress, String userAgent) {
        Map<String, Object> details = new HashMap<>();
        details.put("username", user.getUsername());
        details.put("email", user.getEmail());
        details.put("registerTime", LocalDateTime.now());
        
        log(user.getId(), AuditLog.Actions.REGISTER, AuditLog.ResourceTypes.USER,
            user.getId(), toJson(details), ipAddress, userAgent);
    }
    
    @Override
    public void logPasswordCreated(Password password) {
        Map<String, Object> details = new HashMap<>();
        details.put("title", password.getTitle());
        details.put("website", password.getWebsite());
        details.put("hasTotpEnabled", password.hasTotpEnabled());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.CREATE_PASSWORD,
                            AuditLog.ResourceTypes.PASSWORD, password.getId(), toJson(details));
    }
    
    @Override
    public void logPasswordUpdated(Password password) {
        Map<String, Object> details = new HashMap<>();
        details.put("title", password.getTitle());
        details.put("website", password.getWebsite());
        details.put("hasTotpEnabled", password.hasTotpEnabled());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.UPDATE_PASSWORD,
                            AuditLog.ResourceTypes.PASSWORD, password.getId(), toJson(details));
    }
    
    @Override
    public void logPasswordDeleted(Password password) {
        Map<String, Object> details = new HashMap<>();
        details.put("title", password.getTitle());
        details.put("website", password.getWebsite());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.DELETE_PASSWORD,
                            AuditLog.ResourceTypes.PASSWORD, password.getId(), toJson(details));
    }
    
    @Override
    public void logPasswordViewed(Password password) {
        if (!logSensitiveOperations) {
            return;
        }
        
        Map<String, Object> details = new HashMap<>();
        details.put("title", password.getTitle());
        details.put("website", password.getWebsite());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.VIEW_PASSWORD,
                            AuditLog.ResourceTypes.PASSWORD, password.getId(), toJson(details));
    }
    
    @Override
    public void logTotpEnabled(Password password) {
        Map<String, Object> details = new HashMap<>();
        details.put("passwordTitle", password.getTitle());
        details.put("totpIssuer", password.getTotpIssuer());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.ENABLE_TOTP,
                            AuditLog.ResourceTypes.TOTP, password.getId(), toJson(details));
    }
    
    @Override
    public void logTotpDisabled(Password password) {
        Map<String, Object> details = new HashMap<>();
        details.put("passwordTitle", password.getTitle());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.DISABLE_TOTP,
                            AuditLog.ResourceTypes.TOTP, password.getId(), toJson(details));
    }
    
    @Override
    public void logTotpCodeGenerated(Password password) {
        if (!logSensitiveOperations) {
            return;
        }
        
        Map<String, Object> details = new HashMap<>();
        details.put("passwordTitle", password.getTitle());
        details.put("totpIssuer", password.getTotpIssuer());
        
        logWithCurrentRequest(password.getUserId(), AuditLog.Actions.GENERATE_TOTP,
                            AuditLog.ResourceTypes.TOTP, password.getId(), toJson(details));
    }
    
    // Key 相关方法已移除 - 待后续实现
    
    @Override
    public List<AuditLog> getUserAuditLogs(Long userId, LocalDateTime startTime,
                                          LocalDateTime endTime, int page, int size) {
        return auditLogRepository.findByUserIdAndTimeRange(userId, startTime, endTime, page, size);
    }
    
    @Override
    public List<AuditLog> getResourceAuditLogs(String resourceType, Long resourceId, int page, int size) {
        return auditLogRepository.findByResourceTypeAndResourceId(resourceType, resourceId, page, size);
    }
    
    @Override
    public long countUserActions(Long userId, String action, LocalDateTime startTime, LocalDateTime endTime) {
        return auditLogRepository.countByUserIdAndActionAndTimeRange(userId, action, startTime, endTime);
    }
    
    @Override
    public long cleanupExpiredLogs(int retentionDays) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
        return auditLogRepository.deleteByCreatedAtBefore(cutoffTime);
    }
    
    /**
     * 使用当前请求信息记录日志
     */
    private void logWithCurrentRequest(Long userId, String action, String resourceType,
                                     Long resourceId, String details) {
        String ipAddress = getCurrentIpAddress();
        String userAgent = getCurrentUserAgent();
        log(userId, action, resourceType, resourceId, details, ipAddress, userAgent);
    }
    
    /**
     * 获取当前请求的IP地址
     */
    private String getCurrentIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String xForwardedFor = request.getHeader("X-Forwarded-For");
                if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                    return xForwardedFor.split(",")[0].trim();
                }
                return request.getRemoteAddr();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "unknown";
    }
    
    /**
     * 获取当前请求的User-Agent
     */
    private String getCurrentUserAgent() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("User-Agent");
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "unknown";
    }
    
    @Override
    public void logBatchOperation(Long userId, String operation, String resourceType, BatchOperationResult result) {
        Map<String, Object> details = new HashMap<>();
        details.put("operationId", result.getOperationId());
        details.put("operation", operation);
        details.put("resourceType", resourceType);
        details.put("totalItems", result.getTotalItems());
        details.put("successCount", result.getSuccessCount());
        details.put("failureCount", result.getFailureCount());
        details.put("skippedCount", result.getSkippedCount());
        details.put("status", result.getStatus().name());
        details.put("durationMs", result.getDurationMs());

        if (result.getFailureCount() > 0 && !result.getErrors().isEmpty()) {
            // 只记录前5个错误，避免日志过长
            List<BatchOperationResult.BatchOperationError> limitedErrors = result.getErrors()
                .stream()
                .limit(5)
                .toList();
            details.put("errors", limitedErrors);
        }

        logWithCurrentRequest(userId, "BATCH_" + operation,
                            "BATCH_" + resourceType, null, toJson(details));
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            return object.toString();
        }
    }
}
