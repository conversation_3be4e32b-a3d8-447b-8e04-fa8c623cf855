package club.gclmit.securefox.infrastructure.security;

import club.gclmit.securefox.domain.security.EncryptionService;
import org.bouncycastle.crypto.generators.Argon2BytesGenerator;
import org.bouncycastle.crypto.params.Argon2Parameters;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.SecretKeyFactory;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Arrays;

/**
 * 加密服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
public class EncryptionServiceImpl implements EncryptionService {
    
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/GCM/NoPadding";
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final String PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA256";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    
    private final SecureRandom secureRandom;
    private final byte[] masterKey;
    
    // Argon2 配置参数
    @Value("${securefox.encryption.argon2.memory:65536}")
    private int argon2Memory;
    
    @Value("${securefox.encryption.argon2.iterations:3}")
    private int argon2Iterations;
    
    @Value("${securefox.encryption.argon2.parallelism:4}")
    private int argon2Parallelism;
    
    // PBKDF2 配置参数
    @Value("${securefox.encryption.pbkdf2.iterations:100000}")
    private int pbkdf2Iterations;
    
    public EncryptionServiceImpl() {
        this.secureRandom = new SecureRandom();
        // 生成主密钥（实际应用中应该从安全的地方获取）
        this.masterKey = generateMasterKey();
    }
    
    @Override
    public String hashPassword(String password, String salt) {
        try {
            byte[] saltBytes = Base64.getDecoder().decode(salt);
            byte[] passwordBytes = password.getBytes(StandardCharsets.UTF_8);
            
            Argon2Parameters params = new Argon2Parameters.Builder(Argon2Parameters.ARGON2_id)
                    .withMemoryAsKB(argon2Memory)
                    .withIterations(argon2Iterations)
                    .withParallelism(argon2Parallelism)
                    .withSalt(saltBytes)
                    .build();
            
            Argon2BytesGenerator generator = new Argon2BytesGenerator();
            generator.init(params);
            
            byte[] hash = new byte[32]; // 256 bits
            generator.generateBytes(passwordBytes, hash);
            
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("密码哈希失败", e);
        }
    }
    
    @Override
    public boolean verifyPassword(String password, String hash, String salt) {
        try {
            String computedHash = hashPassword(password, salt);
            return MessageDigest.isEqual(
                Base64.getDecoder().decode(hash),
                Base64.getDecoder().decode(computedHash)
            );
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public String generateSalt() {
        byte[] salt = new byte[32]; // 256 bits
        secureRandom.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    @Override
    public String encrypt(String plaintext) {
        return encryptWithKey(plaintext, Base64.getEncoder().encodeToString(masterKey));
    }
    
    @Override
    public String decrypt(String ciphertext) {
        return decryptWithKey(ciphertext, Base64.getEncoder().encodeToString(masterKey));
    }
    
    @Override
    public String encryptWithKey(String plaintext, String key) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            
            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            secureRandom.nextBytes(iv);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);
            
            byte[] plaintextBytes = plaintext.getBytes(StandardCharsets.UTF_8);
            byte[] cipherBytes = cipher.doFinal(plaintextBytes);
            
            // 组合IV和密文
            byte[] encryptedData = new byte[GCM_IV_LENGTH + cipherBytes.length];
            System.arraycopy(iv, 0, encryptedData, 0, GCM_IV_LENGTH);
            System.arraycopy(cipherBytes, 0, encryptedData, GCM_IV_LENGTH, cipherBytes.length);
            
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }
    
    @Override
    public String decryptWithKey(String ciphertext, String key) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            
            byte[] encryptedData = Base64.getDecoder().decode(ciphertext);
            
            // 提取IV和密文
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] cipherBytes = new byte[encryptedData.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedData, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedData, GCM_IV_LENGTH, cipherBytes, 0, cipherBytes.length);
            
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);
            
            byte[] plaintextBytes = cipher.doFinal(cipherBytes);
            return new String(plaintextBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }
    
    @Override
    public String generateRandomKey(int length) {
        byte[] key = new byte[length];
        secureRandom.nextBytes(key);
        return Base64.getEncoder().encodeToString(key);
    }
    
    @Override
    public String deriveKey(String password, String salt, int iterations, int keyLength) {
        try {
            byte[] saltBytes = Base64.getDecoder().decode(salt);
            PBEKeySpec spec = new PBEKeySpec(password.toCharArray(), saltBytes, iterations, keyLength);
            SecretKeyFactory factory = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM);
            SecretKey key = factory.generateSecret(spec);
            
            // 清除密码
            spec.clearPassword();
            
            return Base64.getEncoder().encodeToString(key.getEncoded());
        } catch (Exception e) {
            throw new RuntimeException("密钥派生失败", e);
        }
    }
    
    @Override
    public String generateSecureRandomString(int length) {
        String charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?";
        StringBuilder result = new StringBuilder(length);
        
        for (int i = 0; i < length; i++) {
            int index = secureRandom.nextInt(charset.length());
            result.append(charset.charAt(index));
        }
        
        return result.toString();
    }
    
    @Override
    public String sha256Hash(String data) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("SHA-256哈希失败", e);
        }
    }
    
    @Override
    public String hmacSha256(String data, String key) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(key);
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, HMAC_ALGORITHM);
            
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            mac.init(secretKey);
            
            byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            throw new RuntimeException("HMAC-SHA256计算失败", e);
        }
    }
    
    /**
     * 生成主密钥
     * 
     * @return 主密钥字节数组
     */
    private byte[] generateMasterKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGenerator.init(256); // AES-256
            SecretKey secretKey = keyGenerator.generateKey();
            return secretKey.getEncoded();
        } catch (Exception e) {
            throw new RuntimeException("主密钥生成失败", e);
        }
    }
}
