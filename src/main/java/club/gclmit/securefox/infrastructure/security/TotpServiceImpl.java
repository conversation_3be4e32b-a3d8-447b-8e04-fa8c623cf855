package club.gclmit.securefox.infrastructure.security;

import club.gclmit.securefox.domain.security.TotpService;
import club.gclmit.securefox.domain.vault.TotpConfig;
import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import com.warrenstrange.googleauth.GoogleAuthenticatorConfig;
import com.warrenstrange.googleauth.HmacHashFunction;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import org.apache.commons.codec.binary.Base32;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * TOTP服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
public class TotpServiceImpl implements TotpService {
    
    private final GoogleAuthenticator googleAuthenticator;
    private final SecureRandom secureRandom;
    private final Base32 base32;
    
    @Value("${securefox.totp.time-tolerance:1}")
    private int defaultTimeTolerance;
    
    public TotpServiceImpl() {
        // 配置Google Authenticator
        GoogleAuthenticatorConfig config = new GoogleAuthenticatorConfig.GoogleAuthenticatorConfigBuilder()
                .setTimeStepSizeInMillis(TimeUnit.SECONDS.toMillis(30))
                .setWindowSize(1) // 允许前后1个时间窗口
                .setCodeDigits(6)
                .setHmacHashFunction(HmacHashFunction.HmacSHA1)
                .build();
        
        this.googleAuthenticator = new GoogleAuthenticator(config);
        this.secureRandom = new SecureRandom();
        this.base32 = new Base32();
    }
    
    @Override
    public String generateCode(TotpConfig config) {
        validateConfig(config);
        
        long timeStep = getCurrentTimeStep(config.getPeriod());
        return generateCodeForTimeStep(config.getSecret(), timeStep, 
                                     config.getDigits(), config.getAlgorithm());
    }
    
    @Override
    public boolean validateCode(TotpConfig config, String code) {
        return validateCodeWithTolerance(config, code, defaultTimeTolerance);
    }
    
    @Override
    public boolean validateCodeWithTolerance(TotpConfig config, String code, int tolerance) {
        validateConfig(config);
        
        if (code == null || code.trim().isEmpty()) {
            return false;
        }
        
        long currentTimeStep = getCurrentTimeStep(config.getPeriod());
        
        // 检查当前时间步长和前后容差范围内的时间步长
        for (int i = -tolerance; i <= tolerance; i++) {
            long timeStep = currentTimeStep + i;
            String expectedCode = generateCodeForTimeStep(config.getSecret(), timeStep,
                                                        config.getDigits(), config.getAlgorithm());
            if (code.equals(expectedCode)) {
                return true;
            }
        }
        
        return false;
    }
    
    @Override
    public int getRemainingSeconds(TotpConfig config) {
        long currentTime = System.currentTimeMillis() / 1000;
        int period = config.getPeriod();
        return period - (int) (currentTime % period);
    }
    
    @Override
    public String generateSecret() {
        GoogleAuthenticatorKey key = googleAuthenticator.createCredentials();
        return key.getKey();
    }
    
    @Override
    public String generateQrCodeImage(String otpAuthUrl) {
        return generateQrCodeImage(otpAuthUrl, 200, 200);
    }
    
    @Override
    public String generateQrCodeImage(String otpAuthUrl, int width, int height) {
        try {
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(otpAuthUrl, BarcodeFormat.QR_CODE, width, height);
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
            
            byte[] imageBytes = outputStream.toByteArray();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
        } catch (WriterException | IOException e) {
            throw new RuntimeException("二维码生成失败", e);
        }
    }
    
    @Override
    public boolean isValidSecret(String secret) {
        if (secret == null || secret.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 检查是否为有效的Base32编码
            base32.decode(secret.toUpperCase());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public long getCurrentTimeStep(int period) {
        return System.currentTimeMillis() / 1000 / period;
    }
    
    @Override
    public String generateCodeForTimeStep(String secret, long timeStep, int digits, String algorithm) {
        try {
            // 解码Base32密钥
            byte[] keyBytes = base32.decode(secret.toUpperCase());
            
            // 将时间步长转换为8字节数组
            ByteBuffer buffer = ByteBuffer.allocate(8);
            buffer.putLong(timeStep);
            byte[] timeBytes = buffer.array();
            
            // 选择HMAC算法
            String hmacAlgorithm = getHmacAlgorithm(algorithm);
            
            // 计算HMAC
            Mac mac = Mac.getInstance(hmacAlgorithm);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, hmacAlgorithm);
            mac.init(keySpec);
            byte[] hmacResult = mac.doFinal(timeBytes);
            
            // 动态截取
            int offset = hmacResult[hmacResult.length - 1] & 0x0F;
            int code = ((hmacResult[offset] & 0x7F) << 24) |
                      ((hmacResult[offset + 1] & 0xFF) << 16) |
                      ((hmacResult[offset + 2] & 0xFF) << 8) |
                      (hmacResult[offset + 3] & 0xFF);
            
            // 取模并格式化
            int modulus = (int) Math.pow(10, digits);
            code = code % modulus;
            
            return String.format("%0" + digits + "d", code);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("TOTP验证码生成失败", e);
        }
    }
    
    /**
     * 验证TOTP配置
     * 
     * @param config TOTP配置
     */
    private void validateConfig(TotpConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("TOTP配置不能为空");
        }
        
        if (!isValidSecret(config.getSecret())) {
            throw new IllegalArgumentException("TOTP密钥格式无效");
        }
        
        if (config.getDigits() < 6 || config.getDigits() > 8) {
            throw new IllegalArgumentException("验证码位数必须在6-8之间");
        }
        
        if (config.getPeriod() < 15 || config.getPeriod() > 300) {
            throw new IllegalArgumentException("时间间隔必须在15-300秒之间");
        }
    }
    
    /**
     * 获取HMAC算法名称
     * 
     * @param algorithm 算法名称
     * @return HMAC算法名称
     */
    private String getHmacAlgorithm(String algorithm) {
        switch (algorithm.toUpperCase()) {
            case "SHA1":
                return "HmacSHA1";
            case "SHA256":
                return "HmacSHA256";
            case "SHA512":
                return "HmacSHA512";
            default:
                return "HmacSHA1";
        }
    }
}
