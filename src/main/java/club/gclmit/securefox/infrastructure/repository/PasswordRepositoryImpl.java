package club.gclmit.securefox.infrastructure.repository;

import club.gclmit.securefox.domain.vault.Password;
import club.gclmit.securefox.domain.vault.PasswordRepository;
import club.gclmit.securefox.infrastructure.repository.mapper.PasswordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 密码仓储实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Repository
public class PasswordRepositoryImpl implements PasswordRepository {
    
    private final PasswordMapper passwordMapper;
    
    public PasswordRepositoryImpl(PasswordMapper passwordMapper) {
        this.passwordMapper = passwordMapper;
    }
    
    @Override
    public Password save(Password password) {
        if (password.getId() == null) {
            passwordMapper.insert(password);
        } else {
            passwordMapper.updateById(password);
        }
        return password;
    }
    
    @Override
    public Optional<Password> findById(Long id) {
        Password password = passwordMapper.selectById(id);
        return Optional.ofNullable(password);
    }
    
    @Override
    public Optional<Password> findByIdAndUserId(Long id, Long userId) {
        LambdaQueryWrapper<Password> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Password::getId, id)
                   .eq(Password::getUserId, userId)
                   .eq(Password::getDeleted, 0);
        
        Password password = passwordMapper.selectOne(queryWrapper);
        return Optional.ofNullable(password);
    }
    
    @Override
    public List<Password> findByUserId(Long userId) {
        LambdaQueryWrapper<Password> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Password::getUserId, userId)
                   .eq(Password::getDeleted, 0)
                   .orderByDesc(Password::getUpdatedAt);
        
        return passwordMapper.selectList(queryWrapper);
    }
    
    @Override
    public List<Password> findByUserIdAndCategoryId(Long userId, Long categoryId) {
        LambdaQueryWrapper<Password> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Password::getUserId, userId)
                   .eq(Password::getCategoryId, categoryId)
                   .eq(Password::getDeleted, 0)
                   .orderByDesc(Password::getUpdatedAt);
        
        return passwordMapper.selectList(queryWrapper);
    }
    
    @Override
    public List<Password> searchByKeyword(Long userId, String keyword) {
        return passwordMapper.searchByKeyword(userId, keyword);
    }
    
    @Override
    public List<Password> findByUserIdWithPagination(Long userId, int page, int size) {
        int offset = (page - 1) * size;
        return passwordMapper.findByUserIdWithPagination(userId, offset, size);
    }
    
    @Override
    public long countByUserId(Long userId) {
        LambdaQueryWrapper<Password> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Password::getUserId, userId)
                   .eq(Password::getDeleted, 0);
        
        return passwordMapper.selectCount(queryWrapper);
    }
    
    @Override
    public long countByUserIdAndCategoryId(Long userId, Long categoryId) {
        LambdaQueryWrapper<Password> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Password::getUserId, userId)
                   .eq(Password::getCategoryId, categoryId)
                   .eq(Password::getDeleted, 0);
        
        return passwordMapper.selectCount(queryWrapper);
    }
    
    @Override
    public List<Password> findByUserIdAndTotpEnabled(Long userId) {
        return passwordMapper.findByUserIdAndTotpEnabled(userId);
    }
    
    @Override
    public List<Password> findByUserIdAndSyncVersionGreaterThan(Long userId, Integer sinceVersion) {
        return passwordMapper.findByUserIdAndSyncVersionGreaterThan(userId, sinceVersion);
    }
    
    @Override
    public void deleteById(Long id) {
        passwordMapper.deleteById(id);
    }
    
    @Override
    public void deleteByUserId(Long userId) {
        LambdaQueryWrapper<Password> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Password::getUserId, userId);
        
        passwordMapper.delete(queryWrapper);
    }
}
