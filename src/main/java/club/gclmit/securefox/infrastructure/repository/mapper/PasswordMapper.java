package club.gclmit.securefox.infrastructure.repository.mapper;

import club.gclmit.securefox.domain.vault.Password;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 密码Mapper接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Mapper
public interface PasswordMapper extends BaseMapper<Password> {
    
    /**
     * 搜索密码
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 密码列表
     */
    @Select("SELECT * FROM passwords WHERE user_id = #{userId} AND deleted = 0 " +
            "AND (title LIKE CONCAT('%', #{keyword}, '%') " +
            "OR username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR website LIKE CONCAT('%', #{keyword}, '%') " +
            "OR notes LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY updated_at DESC")
    List<Password> searchByKeyword(@Param("userId") Long userId, @Param("keyword") String keyword);
    
    /**
     * 分页查询用户密码
     * 
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 密码列表
     */
    @Select("SELECT * FROM passwords WHERE user_id = #{userId} AND deleted = 0 " +
            "ORDER BY updated_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<Password> findByUserIdWithPagination(@Param("userId") Long userId, 
                                            @Param("offset") int offset, 
                                            @Param("limit") int limit);
    
    /**
     * 查找启用了TOTP的密码
     * 
     * @param userId 用户ID
     * @return 密码列表
     */
    @Select("SELECT * FROM passwords WHERE user_id = #{userId} AND deleted = 0 " +
            "AND totp_secret IS NOT NULL AND totp_secret != '' " +
            "ORDER BY updated_at DESC")
    List<Password> findByUserIdAndTotpEnabled(@Param("userId") Long userId);
    
    /**
     * 根据同步版本查找变更的密码
     * 
     * @param userId 用户ID
     * @param sinceVersion 起始版本号
     * @return 密码列表
     */
    @Select("SELECT * FROM passwords WHERE user_id = #{userId} " +
            "AND sync_version > #{sinceVersion} " +
            "ORDER BY sync_version ASC")
    List<Password> findByUserIdAndSyncVersionGreaterThan(@Param("userId") Long userId, 
                                                       @Param("sinceVersion") Integer sinceVersion);
}
