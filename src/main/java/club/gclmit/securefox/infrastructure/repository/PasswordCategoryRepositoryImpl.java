package club.gclmit.securefox.infrastructure.repository;

import club.gclmit.securefox.domain.vault.PasswordCategory;
import club.gclmit.securefox.domain.vault.PasswordCategoryRepository;
import club.gclmit.securefox.infrastructure.repository.mapper.PasswordCategoryMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 密码分类仓储实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Repository
public class PasswordCategoryRepositoryImpl implements PasswordCategoryRepository {
    
    private final PasswordCategoryMapper categoryMapper;
    
    public PasswordCategoryRepositoryImpl(PasswordCategoryMapper categoryMapper) {
        this.categoryMapper = categoryMapper;
    }
    
    @Override
    public PasswordCategory save(PasswordCategory category) {
        if (category.getId() == null) {
            categoryMapper.insert(category);
        } else {
            categoryMapper.updateById(category);
        }
        return category;
    }
    
    @Override
    public Optional<PasswordCategory> findById(Long id) {
        PasswordCategory category = categoryMapper.selectById(id);
        return Optional.ofNullable(category);
    }
    
    @Override
    public Optional<PasswordCategory> findByIdAndUserId(Long id, Long userId) {
        LambdaQueryWrapper<PasswordCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PasswordCategory::getId, id)
                   .eq(PasswordCategory::getUserId, userId)
                   .eq(PasswordCategory::getDeleted, 0);
        
        PasswordCategory category = categoryMapper.selectOne(queryWrapper);
        return Optional.ofNullable(category);
    }
    
    @Override
    public List<PasswordCategory> findByUserId(Long userId) {
        LambdaQueryWrapper<PasswordCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PasswordCategory::getUserId, userId)
                   .eq(PasswordCategory::getDeleted, 0)
                   .orderByAsc(PasswordCategory::getCreatedAt);
        
        return categoryMapper.selectList(queryWrapper);
    }
    
    @Override
    public Optional<PasswordCategory> findByUserIdAndName(Long userId, String name) {
        LambdaQueryWrapper<PasswordCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PasswordCategory::getUserId, userId)
                   .eq(PasswordCategory::getName, name)
                   .eq(PasswordCategory::getDeleted, 0);
        
        PasswordCategory category = categoryMapper.selectOne(queryWrapper);
        return Optional.ofNullable(category);
    }
    
    @Override
    public boolean existsByUserIdAndName(Long userId, String name) {
        LambdaQueryWrapper<PasswordCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PasswordCategory::getUserId, userId)
                   .eq(PasswordCategory::getName, name)
                   .eq(PasswordCategory::getDeleted, 0);
        
        return categoryMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public long countByUserId(Long userId) {
        LambdaQueryWrapper<PasswordCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PasswordCategory::getUserId, userId)
                   .eq(PasswordCategory::getDeleted, 0);
        
        return categoryMapper.selectCount(queryWrapper);
    }
    
    @Override
    public void deleteById(Long id) {
        categoryMapper.deleteById(id);
    }
    
    @Override
    public void deleteByUserId(Long userId) {
        LambdaQueryWrapper<PasswordCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PasswordCategory::getUserId, userId);
        
        categoryMapper.delete(queryWrapper);
    }
    
    @Override
    public List<PasswordCategory> saveAll(List<PasswordCategory> categories) {
        for (PasswordCategory category : categories) {
            save(category);
        }
        return categories;
    }
}
