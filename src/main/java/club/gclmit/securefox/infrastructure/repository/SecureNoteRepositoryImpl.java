package club.gclmit.securefox.infrastructure.repository;

import club.gclmit.securefox.domain.note.NoteType;
import club.gclmit.securefox.domain.note.SecureNote;
import club.gclmit.securefox.domain.note.SecureNoteRepository;
import club.gclmit.securefox.infrastructure.repository.mapper.SecureNoteMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 安全笔记仓储实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Repository
@RequiredArgsConstructor
public class SecureNoteRepositoryImpl implements SecureNoteRepository {
    
    private final SecureNoteMapper secureNoteMapper;
    
    @Override
    public SecureNote save(SecureNote note) {
        if (note.getId() == null) {
            secureNoteMapper.insert(note);
        } else {
            secureNoteMapper.updateById(note);
        }
        return note;
    }
    
    @Override
    public Optional<SecureNote> findById(Long id) {
        SecureNote note = secureNoteMapper.selectById(id);
        return Optional.ofNullable(note);
    }
    
    @Override
    public Optional<SecureNote> findByIdAndUserId(Long id, Long userId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getId, id)
               .eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getDeleted, 0);
        
        SecureNote note = secureNoteMapper.selectOne(wrapper);
        return Optional.ofNullable(note);
    }
    
    @Override
    public List<SecureNote> findByUserId(Long userId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getDeleted, 0)
               .orderByDesc(SecureNote::getPinned)
               .orderByDesc(SecureNote::getUpdatedAt);
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public List<SecureNote> findByUserIdAndNoteType(Long userId, NoteType noteType) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getNoteType, noteType)
               .eq(SecureNote::getDeleted, 0)
               .orderByDesc(SecureNote::getPinned)
               .orderByDesc(SecureNote::getUpdatedAt);
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public List<SecureNote> findByUserIdAndCategoryId(Long userId, Long categoryId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getCategoryId, categoryId)
               .eq(SecureNote::getDeleted, 0)
               .orderByDesc(SecureNote::getPinned)
               .orderByDesc(SecureNote::getUpdatedAt);
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public List<SecureNote> searchByKeyword(Long userId, String keyword) {
        QueryWrapper<SecureNote> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .eq("deleted", 0)
               .and(w -> w.like("title", keyword).or().like("tags", keyword))
               .orderByDesc("pinned")
               .orderByDesc("updated_at");
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public List<SecureNote> findByUserIdWithPagination(Long userId, int page, int size) {
        Page<SecureNote> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getDeleted, 0)
               .orderByDesc(SecureNote::getPinned)
               .orderByDesc(SecureNote::getUpdatedAt);
        
        Page<SecureNote> result = secureNoteMapper.selectPage(pageParam, wrapper);
        return result.getRecords();
    }
    
    @Override
    public List<SecureNote> findByUserIdAndFavorite(Long userId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getFavorite, 1)
               .eq(SecureNote::getDeleted, 0)
               .orderByDesc(SecureNote::getUpdatedAt);
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public List<SecureNote> findByUserIdAndPinned(Long userId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getPinned, 1)
               .eq(SecureNote::getDeleted, 0)
               .orderByDesc(SecureNote::getUpdatedAt);
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public long countByUserId(Long userId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getDeleted, 0);
        
        return secureNoteMapper.selectCount(wrapper);
    }
    
    @Override
    public long countByUserIdAndNoteType(Long userId, NoteType noteType) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getNoteType, noteType)
               .eq(SecureNote::getDeleted, 0);
        
        return secureNoteMapper.selectCount(wrapper);
    }
    
    @Override
    public long countByUserIdAndCategoryId(Long userId, Long categoryId) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .eq(SecureNote::getCategoryId, categoryId)
               .eq(SecureNote::getDeleted, 0);
        
        return secureNoteMapper.selectCount(wrapper);
    }
    
    @Override
    public List<SecureNote> findByUserIdAndSyncVersionGreaterThan(Long userId, Integer sinceVersion) {
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId)
               .gt(SecureNote::getSyncVersion, sinceVersion)
               .orderByAsc(SecureNote::getSyncVersion);
        
        return secureNoteMapper.selectList(wrapper);
    }
    
    @Override
    public void deleteById(Long id) {
        // 逻辑删除
        SecureNote note = new SecureNote();
        note.setId(id);
        note.setDeleted(1);
        secureNoteMapper.updateById(note);
    }
    
    @Override
    public void deleteByUserId(Long userId) {
        // 批量逻辑删除
        LambdaQueryWrapper<SecureNote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SecureNote::getUserId, userId);
        
        SecureNote updateNote = new SecureNote();
        updateNote.setDeleted(1);
        secureNoteMapper.update(updateNote, wrapper);
    }
}
