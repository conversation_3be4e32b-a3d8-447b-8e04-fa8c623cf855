package club.gclmit.securefox.infrastructure.repository.mapper;

import club.gclmit.securefox.domain.audit.AuditLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志Mapper接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Mapper
public interface AuditLogMapper extends BaseMapper<AuditLog> {
    
    /**
     * 根据用户ID和时间范围查询审计日志
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 审计日志列表
     */
    @Select("SELECT * FROM audit_logs WHERE user_id = #{userId} " +
            "AND created_at >= #{startTime} AND created_at <= #{endTime} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<AuditLog> findByUserIdAndTimeRange(@Param("userId") Long userId,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime,
                                          @Param("offset") int offset,
                                          @Param("limit") int limit);
    
    /**
     * 根据资源类型和资源ID查询审计日志
     * 
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 审计日志列表
     */
    @Select("SELECT * FROM audit_logs WHERE resource_type = #{resourceType} " +
            "AND resource_id = #{resourceId} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<AuditLog> findByResourceTypeAndResourceId(@Param("resourceType") String resourceType,
                                                 @Param("resourceId") Long resourceId,
                                                 @Param("offset") int offset,
                                                 @Param("limit") int limit);
    
    /**
     * 统计用户在指定时间范围内的操作次数
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select("SELECT COUNT(*) FROM audit_logs WHERE user_id = #{userId} " +
            "AND action = #{action} " +
            "AND created_at >= #{startTime} AND created_at <= #{endTime}")
    long countByUserIdAndActionAndTimeRange(@Param("userId") Long userId,
                                          @Param("action") String action,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 删除指定时间之前的审计日志
     * 
     * @param cutoffTime 截止时间
     * @return 删除的日志数量
     */
    @Delete("DELETE FROM audit_logs WHERE created_at < #{cutoffTime}")
    long deleteByCreatedAtBefore(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据用户ID查询最近的审计日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 审计日志列表
     */
    @Select("SELECT * FROM audit_logs WHERE user_id = #{userId} " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<AuditLog> findRecentByUserId(@Param("userId") Long userId, @Param("limit") int limit);
    
    /**
     * 查询指定操作类型的审计日志
     * 
     * @param action 操作类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 审计日志列表
     */
    @Select("SELECT * FROM audit_logs WHERE action = #{action} " +
            "ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<AuditLog> findByAction(@Param("action") String action,
                              @Param("offset") int offset,
                              @Param("limit") int limit);
}
