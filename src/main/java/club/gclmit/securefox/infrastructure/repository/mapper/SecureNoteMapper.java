package club.gclmit.securefox.infrastructure.repository.mapper;

import club.gclmit.securefox.domain.note.SecureNote;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 安全笔记 Mapper 接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Mapper
public interface SecureNoteMapper extends BaseMapper<SecureNote> {
    
    // MyBatis-Plus 提供了基础的 CRUD 操作
    // 如需要复杂查询，可以在这里添加自定义方法
    
}
