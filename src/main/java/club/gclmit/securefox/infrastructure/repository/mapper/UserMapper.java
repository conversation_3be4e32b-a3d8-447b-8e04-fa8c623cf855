package club.gclmit.securefox.infrastructure.repository.mapper;

import club.gclmit.securefox.domain.user.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 更新用户最后登录时间
     * 
     * @param id 用户ID
     * @param lastLoginTime 最后登录时间
     * @return 影响行数
     */
    @Update("UPDATE users SET updated_at = #{lastLoginTime} WHERE id = #{id}")
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") LocalDateTime lastLoginTime);
}
