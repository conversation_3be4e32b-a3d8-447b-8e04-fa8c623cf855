package club.gclmit.securefox.infrastructure.repository;

import club.gclmit.securefox.domain.audit.AuditLog;
import club.gclmit.securefox.infrastructure.audit.AuditLogRepository;
import club.gclmit.securefox.infrastructure.repository.mapper.AuditLogMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志仓储实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Repository
public class AuditLogRepositoryImpl implements AuditLogRepository {
    
    private final AuditLogMapper auditLogMapper;
    
    public AuditLogRepositoryImpl(AuditLogMapper auditLogMapper) {
        this.auditLogMapper = auditLogMapper;
    }
    
    @Override
    public AuditLog save(AuditLog auditLog) {
        auditLogMapper.insert(auditLog);
        return auditLog;
    }
    
    @Override
    public List<AuditLog> findByUserIdAndTimeRange(Long userId, LocalDateTime startTime,
                                                  LocalDateTime endTime, int page, int size) {
        int offset = (page - 1) * size;
        return auditLogMapper.findByUserIdAndTimeRange(userId, startTime, endTime, offset, size);
    }
    
    @Override
    public List<AuditLog> findByResourceTypeAndResourceId(String resourceType, Long resourceId,
                                                         int page, int size) {
        int offset = (page - 1) * size;
        return auditLogMapper.findByResourceTypeAndResourceId(resourceType, resourceId, offset, size);
    }
    
    @Override
    public long countByUserIdAndActionAndTimeRange(Long userId, String action,
                                                  LocalDateTime startTime, LocalDateTime endTime) {
        return auditLogMapper.countByUserIdAndActionAndTimeRange(userId, action, startTime, endTime);
    }
    
    @Override
    public long deleteByCreatedAtBefore(LocalDateTime cutoffTime) {
        return auditLogMapper.deleteByCreatedAtBefore(cutoffTime);
    }
    
    @Override
    public List<AuditLog> findRecentByUserId(Long userId, int limit) {
        return auditLogMapper.findRecentByUserId(userId, limit);
    }
    
    @Override
    public List<AuditLog> findByAction(String action, int page, int size) {
        int offset = (page - 1) * size;
        return auditLogMapper.findByAction(action, offset, size);
    }
    
    @Override
    public long count() {
        return auditLogMapper.selectCount(new LambdaQueryWrapper<>());
    }
}
