package club.gclmit.securefox.domain.security;

import club.gclmit.securefox.domain.vault.TotpConfig;

/**
 * TOTP服务接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public interface TotpService {
    
    /**
     * 生成TOTP验证码
     * 
     * @param config TOTP配置
     * @return 6位或8位验证码
     */
    String generateCode(TotpConfig config);
    
    /**
     * 验证TOTP验证码
     * 
     * @param config TOTP配置
     * @param code 用户输入的验证码
     * @return 验证结果
     */
    boolean validateCode(TotpConfig config, String code);
    
    /**
     * 验证TOTP验证码（带时间容差）
     * 
     * @param config TOTP配置
     * @param code 用户输入的验证码
     * @param tolerance 时间容差（允许前后多少个时间窗口）
     * @return 验证结果
     */
    boolean validateCodeWithTolerance(TotpConfig config, String code, int tolerance);
    
    /**
     * 获取当前验证码剩余有效时间（秒）
     * 
     * @param config TOTP配置
     * @return 剩余秒数
     */
    int getRemainingSeconds(TotpConfig config);
    
    /**
     * 生成TOTP密钥
     * 
     * @return Base32编码的密钥
     */
    String generateSecret();
    
    /**
     * 生成二维码图片（Base64格式）
     * 
     * @param otpAuthUrl OTP Auth URL
     * @return Base64编码的PNG图片
     */
    String generateQrCodeImage(String otpAuthUrl);
    
    /**
     * 生成二维码图片（Base64格式）
     * 
     * @param otpAuthUrl OTP Auth URL
     * @param width 图片宽度
     * @param height 图片高度
     * @return Base64编码的PNG图片
     */
    String generateQrCodeImage(String otpAuthUrl, int width, int height);
    
    /**
     * 验证TOTP密钥格式
     * 
     * @param secret TOTP密钥
     * @return 是否有效
     */
    boolean isValidSecret(String secret);
    
    /**
     * 获取当前时间步长
     * 
     * @param period 时间间隔（秒）
     * @return 时间步长
     */
    long getCurrentTimeStep(int period);
    
    /**
     * 根据时间步长生成验证码
     * 
     * @param secret TOTP密钥
     * @param timeStep 时间步长
     * @param digits 验证码位数
     * @param algorithm 算法
     * @return 验证码
     */
    String generateCodeForTimeStep(String secret, long timeStep, int digits, String algorithm);
}
