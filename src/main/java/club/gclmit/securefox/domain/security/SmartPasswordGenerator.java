package club.gclmit.securefox.domain.security;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能密码生成器
 * 支持多种密码生成策略，确保生成的密码既安全又易用
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Service
public class SmartPasswordGenerator {
    
    private final SecureRandom secureRandom = new SecureRandom();
    private final AdvancedPasswordAnalyzer passwordAnalyzer;
    
    // 字符集定义
    private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMBERS = "0123456789";
    private static final String SYMBOLS = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    private static final String SIMILAR_CHARS = "il1Lo0O"; // 相似字符
    private static final String AMBIGUOUS_CHARS = "{}[]()/\\'\"`~,;.<>"; // 容易混淆的字符
    
    // 记忆友好的单词库
    private static final String[] MEMORABLE_WORDS = {
        "apple", "brave", "cloud", "dream", "eagle", "flame", "grace", "happy",
        "island", "jungle", "knight", "light", "magic", "noble", "ocean", "peace",
        "quiet", "river", "storm", "tiger", "unity", "voice", "water", "youth",
        "zebra", "anchor", "bridge", "castle", "dragon", "forest", "garden", "harbor"
    };
    
    public SmartPasswordGenerator(AdvancedPasswordAnalyzer passwordAnalyzer) {
        this.passwordAnalyzer = passwordAnalyzer;
    }
    
    /**
     * 基于配置生成密码
     * 
     * @param config 生成配置
     * @return 生成的密码
     */
    public String generatePassword(PasswordGenerationConfig config) {
        String password;
        int attempts = 0;
        int maxAttempts = 100;
        
        do {
            password = generatePasswordInternal(config);
            attempts++;
            
            if (attempts >= maxAttempts) {
                break; // 防止无限循环
            }
            
        } while (!isPasswordAcceptable(password, config));
        
        return password;
    }
    
    /**
     * 生成记忆友好的密码
     * 
     * @param wordCount 单词数量
     * @param separator 分隔符
     * @param includeNumbers 是否包含数字
     * @param capitalizeWords 是否首字母大写
     * @return 生成的密码
     */
    public String generateMemorablePassword(int wordCount, String separator, 
                                          boolean includeNumbers, boolean capitalizeWords) {
        if (wordCount < 2 || wordCount > 6) {
            throw new IllegalArgumentException("单词数量必须在2-6之间");
        }
        
        List<String> selectedWords = new ArrayList<>();
        Set<String> usedWords = new HashSet<>();
        
        // 随机选择不重复的单词
        while (selectedWords.size() < wordCount) {
            String word = MEMORABLE_WORDS[secureRandom.nextInt(MEMORABLE_WORDS.length)];
            if (!usedWords.contains(word)) {
                if (capitalizeWords) {
                    word = capitalizeWord(word);
                }
                selectedWords.add(word);
                usedWords.add(word.toLowerCase());
            }
        }
        
        String password = String.join(separator, selectedWords);
        
        // 添加数字
        if (includeNumbers) {
            int numberLength = 2 + secureRandom.nextInt(3); // 2-4位数字
            StringBuilder numbers = new StringBuilder();
            for (int i = 0; i < numberLength; i++) {
                numbers.append(secureRandom.nextInt(10));
            }
            password += numbers.toString();
        }
        
        return password;
    }
    
    /**
     * 生成符合特定强度要求的密码
     * 
     * @param minEntropy 最小熵值
     * @param maxLength 最大长度
     * @return 生成的密码
     */
    public String generatePasswordWithMinEntropy(double minEntropy, int maxLength) {
        PasswordGenerationConfig config = PasswordGenerationConfig.builder()
            .length(12)
            .includeUppercase(true)
            .includeLowercase(true)
            .includeNumbers(true)
            .includeSymbols(true)
            .excludeSimilarChars(true)
            .minEntropy(minEntropy)
            .build();
        
        String password = generatePassword(config);
        
        // 如果密码太长，尝试优化
        if (password.length() > maxLength) {
            config.setLength(Math.min(maxLength, 16));
            password = generatePassword(config);
        }
        
        return password;
    }
    
    /**
     * 内部密码生成逻辑
     */
    private String generatePasswordInternal(PasswordGenerationConfig config) {
        StringBuilder charset = new StringBuilder();
        List<String> requiredChars = new ArrayList<>();
        
        // 构建字符集并确保包含必需字符
        if (config.isIncludeUppercase()) {
            String chars = config.isExcludeSimilarChars() ? 
                excludeChars(UPPERCASE, SIMILAR_CHARS) : UPPERCASE;
            charset.append(chars);
            requiredChars.add(getRandomChar(chars));
        }
        
        if (config.isIncludeLowercase()) {
            String chars = config.isExcludeSimilarChars() ? 
                excludeChars(LOWERCASE, SIMILAR_CHARS) : LOWERCASE;
            charset.append(chars);
            requiredChars.add(getRandomChar(chars));
        }
        
        if (config.isIncludeNumbers()) {
            String chars = config.isExcludeSimilarChars() ? 
                excludeChars(NUMBERS, SIMILAR_CHARS) : NUMBERS;
            charset.append(chars);
            requiredChars.add(getRandomChar(chars));
        }
        
        if (config.isIncludeSymbols()) {
            String chars = config.isExcludeAmbiguousChars() ? 
                excludeChars(SYMBOLS, AMBIGUOUS_CHARS) : SYMBOLS;
            charset.append(chars);
            requiredChars.add(getRandomChar(chars));
        }
        
        if (charset.length() == 0) {
            throw new IllegalArgumentException("至少需要选择一种字符类型");
        }
        
        // 生成密码
        StringBuilder password = new StringBuilder();
        String charsetStr = charset.toString();
        
        // 首先添加必需字符
        for (String requiredChar : requiredChars) {
            password.append(requiredChar);
        }
        
        // 填充剩余长度
        while (password.length() < config.getLength()) {
            char randomChar = charsetStr.charAt(secureRandom.nextInt(charsetStr.length()));
            password.append(randomChar);
        }
        
        // 打乱字符顺序
        return shuffleString(password.toString());
    }
    
    /**
     * 检查密码是否符合要求
     */
    private boolean isPasswordAcceptable(String password, PasswordGenerationConfig config) {
        // 检查最小熵值要求
        if (config.getMinEntropy() > 0) {
            double entropy = passwordAnalyzer.analyzePassword(password).getEntropy();
            if (entropy < config.getMinEntropy()) {
                return false;
            }
        }
        
        // 检查是否避免常见模式
        if (config.isAvoidCommonPatterns()) {
            var analysis = passwordAnalyzer.analyzePassword(password);
            return analysis.getWeaknesses().isEmpty() || 
                   analysis.getWeaknesses().stream()
                       .noneMatch(w -> w == AdvancedPasswordAnalyzer.WeaknessType.COMMON_PASSWORD ||
                                      w == AdvancedPasswordAnalyzer.WeaknessType.KEYBOARD_PATTERN);
        }
        
        return true;
    }
    
    // 辅助方法
    private String excludeChars(String source, String toExclude) {
        return source.chars()
            .filter(c -> toExclude.indexOf(c) == -1)
            .mapToObj(c -> String.valueOf((char) c))
            .collect(Collectors.joining());
    }
    
    private String getRandomChar(String charset) {
        return String.valueOf(charset.charAt(secureRandom.nextInt(charset.length())));
    }
    
    private String shuffleString(String input) {
        List<Character> chars = input.chars()
            .mapToObj(c -> (char) c)
            .collect(Collectors.toList());
        Collections.shuffle(chars, secureRandom);
        return chars.stream()
            .map(String::valueOf)
            .collect(Collectors.joining());
    }
    
    private String capitalizeWord(String word) {
        if (word.isEmpty()) return word;
        return Character.toUpperCase(word.charAt(0)) + word.substring(1);
    }
    
    /**
     * 密码生成配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordGenerationConfig {
        @Builder.Default
        private int length = 12;
        
        @Builder.Default
        private boolean includeUppercase = true;
        
        @Builder.Default
        private boolean includeLowercase = true;
        
        @Builder.Default
        private boolean includeNumbers = true;
        
        @Builder.Default
        private boolean includeSymbols = false;
        
        @Builder.Default
        private boolean excludeSimilarChars = true;
        
        @Builder.Default
        private boolean excludeAmbiguousChars = false;
        
        @Builder.Default
        private boolean avoidCommonPatterns = true;
        
        @Builder.Default
        private double minEntropy = 0.0;
        
        public static PasswordGenerationConfig getDefault() {
            return PasswordGenerationConfig.builder().build();
        }
        
        public static PasswordGenerationConfig getHighSecurity() {
            return PasswordGenerationConfig.builder()
                .length(16)
                .includeSymbols(true)
                .minEntropy(60.0)
                .build();
        }
        
        public static PasswordGenerationConfig getUserFriendly() {
            return PasswordGenerationConfig.builder()
                .length(12)
                .includeSymbols(false)
                .excludeSimilarChars(true)
                .excludeAmbiguousChars(true)
                .build();
        }
    }
}
