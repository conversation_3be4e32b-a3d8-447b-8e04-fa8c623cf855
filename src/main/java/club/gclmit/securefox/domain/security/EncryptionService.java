package club.gclmit.securefox.domain.security;

/**
 * 加密服务接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public interface EncryptionService {
    
    /**
     * 使用Argon2算法哈希密码
     * 
     * @param password 明文密码
     * @param salt 盐值
     * @return 哈希后的密码
     */
    String hashPassword(String password, String salt);
    
    /**
     * 验证密码
     * 
     * @param password 明文密码
     * @param hash 哈希值
     * @param salt 盐值
     * @return 验证结果
     */
    boolean verifyPassword(String password, String hash, String salt);
    
    /**
     * 生成随机盐值
     * 
     * @return 盐值
     */
    String generateSalt();
    
    /**
     * 使用AES-256-GCM加密数据
     * 
     * @param plaintext 明文数据
     * @return 加密后的数据（Base64编码）
     */
    String encrypt(String plaintext);
    
    /**
     * 使用AES-256-GCM解密数据
     * 
     * @param ciphertext 密文数据（Base64编码）
     * @return 解密后的明文数据
     */
    String decrypt(String ciphertext);
    
    /**
     * 使用指定密钥加密数据
     * 
     * @param plaintext 明文数据
     * @param key 加密密钥
     * @return 加密后的数据（Base64编码）
     */
    String encryptWithKey(String plaintext, String key);
    
    /**
     * 使用指定密钥解密数据
     * 
     * @param ciphertext 密文数据（Base64编码）
     * @param key 解密密钥
     * @return 解密后的明文数据
     */
    String decryptWithKey(String ciphertext, String key);
    
    /**
     * 生成随机密钥
     * 
     * @param length 密钥长度（字节）
     * @return Base64编码的密钥
     */
    String generateRandomKey(int length);
    
    /**
     * 使用PBKDF2派生密钥
     * 
     * @param password 密码
     * @param salt 盐值
     * @param iterations 迭代次数
     * @param keyLength 密钥长度（位）
     * @return 派生的密钥（Base64编码）
     */
    String deriveKey(String password, String salt, int iterations, int keyLength);
    
    /**
     * 生成安全的随机字符串
     * 
     * @param length 长度
     * @return 随机字符串
     */
    String generateSecureRandomString(int length);
    
    /**
     * 计算数据的SHA-256哈希值
     * 
     * @param data 数据
     * @return 哈希值（十六进制字符串）
     */
    String sha256Hash(String data);
    
    /**
     * 计算数据的HMAC-SHA256值
     * 
     * @param data 数据
     * @param key 密钥
     * @return HMAC值（Base64编码）
     */
    String hmacSha256(String data, String key);
}
