package club.gclmit.securefox.domain.security;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 密码泄露检查服务
 * 使用HaveIBeenPwned API进行密码泄露检查
 * 采用k-anonymity技术保护用户隐私
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Slf4j
@Service
public class PasswordBreachChecker {
    
    private final RestTemplate restTemplate;
    private final boolean breachCheckEnabled;
    private final String apiUrl;
    private final int timeoutMs;
    
    public PasswordBreachChecker(RestTemplate restTemplate,
                                @Value("${securefox.security.breach-check.enabled:true}") boolean breachCheckEnabled,
                                @Value("${securefox.security.breach-check.api-url:https://api.pwnedpasswords.com/range/}") String apiUrl,
                                @Value("${securefox.security.breach-check.timeout:5000}") int timeoutMs) {
        this.restTemplate = restTemplate;
        this.breachCheckEnabled = breachCheckEnabled;
        this.apiUrl = apiUrl;
        this.timeoutMs = timeoutMs;
    }
    
    /**
     * 检查密码是否在已知泄露数据库中
     * 使用k-anonymity保护隐私，只发送SHA-1哈希的前5位
     * 
     * @param password 要检查的密码
     * @return 泄露检查结果
     */
    public BreachCheckResult checkPasswordBreach(String password) {
        if (!breachCheckEnabled) {
            log.debug("密码泄露检查已禁用");
            return BreachCheckResult.disabled();
        }
        
        if (password == null || password.isEmpty()) {
            return BreachCheckResult.error("密码不能为空");
        }
        
        try {
            // 计算SHA-1哈希
            String sha1Hash = calculateSha1Hash(password);
            String prefix = sha1Hash.substring(0, 5).toUpperCase();
            String suffix = sha1Hash.substring(5).toUpperCase();
            
            log.debug("检查密码泄露，哈希前缀: {}", prefix);
            
            // 调用HaveIBeenPwned API
            String response = callPwnedPasswordsApi(prefix);
            
            if (response == null || response.isEmpty()) {
                return BreachCheckResult.safe();
            }
            
            // 解析响应，查找匹配的哈希
            Optional<Integer> breachCount = parseApiResponse(response, suffix);
            
            if (breachCount.isPresent()) {
                log.warn("密码在泄露数据库中发现，出现次数: {}", breachCount.get());
                return BreachCheckResult.breached(breachCount.get());
            } else {
                return BreachCheckResult.safe();
            }
            
        } catch (Exception e) {
            log.error("密码泄露检查失败", e);
            return BreachCheckResult.error("检查服务暂时不可用: " + e.getMessage());
        }
    }
    
    /**
     * 批量检查多个密码
     * 
     * @param passwords 密码列表
     * @return 检查结果列表
     */
    public List<BreachCheckResult> checkMultiplePasswords(List<String> passwords) {
        return passwords.stream()
            .map(this::checkPasswordBreach)
            .toList();
    }
    
    /**
     * 计算密码的SHA-1哈希值
     */
    private String calculateSha1Hash(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] hashBytes = digest.digest(password.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString().toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-1算法不可用", e);
        }
    }
    
    /**
     * 调用HaveIBeenPwned API
     */
    private String callPwnedPasswordsApi(String hashPrefix) {
        try {
            String url = apiUrl + hashPrefix;
            log.debug("调用API: {}", url);
            
            // 设置User-Agent以符合API要求
            return restTemplate.getForObject(url, String.class);
            
        } catch (RestClientException e) {
            log.warn("API调用失败: {}", e.getMessage());
            throw new RuntimeException("无法连接到密码泄露检查服务", e);
        }
    }
    
    /**
     * 解析API响应
     */
    private Optional<Integer> parseApiResponse(String response, String targetSuffix) {
        if (response == null || response.isEmpty()) {
            return Optional.empty();
        }
        
        return Arrays.stream(response.split("\n"))
            .map(String::trim)
            .filter(line -> !line.isEmpty())
            .map(line -> line.split(":"))
            .filter(parts -> parts.length == 2)
            .filter(parts -> parts[0].equals(targetSuffix))
            .map(parts -> {
                try {
                    return Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    log.warn("无法解析泄露次数: {}", parts[1]);
                    return 0;
                }
            })
            .findFirst();
    }
    
    /**
     * 获取密码安全建议
     */
    public String getSecurityAdvice(BreachCheckResult result) {
        if (!result.isEnabled()) {
            return "密码泄露检查已禁用";
        }
        
        if (result.isHasError()) {
            return "无法检查密码安全性：" + result.getErrorMessage();
        }
        
        if (result.isBreached()) {
            if (result.getBreachCount() > 100000) {
                return "⚠️ 严重警告：此密码在数据泄露中出现超过10万次，极不安全，请立即更换！";
            } else if (result.getBreachCount() > 10000) {
                return "⚠️ 高风险：此密码在数据泄露中出现超过1万次，强烈建议更换！";
            } else if (result.getBreachCount() > 1000) {
                return "⚠️ 中等风险：此密码在数据泄露中出现超过1千次，建议更换。";
            } else {
                return "⚠️ 低风险：此密码在数据泄露中出现过，建议考虑更换。";
            }
        } else {
            return "✅ 此密码未在已知数据泄露中发现，相对安全。";
        }
    }
    
    /**
     * 密码泄露检查结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BreachCheckResult {
        private boolean enabled; // 检查是否启用
        private boolean breached; // 是否泄露
        private int breachCount; // 泄露次数
        private boolean hasError; // 是否有错误
        private String errorMessage; // 错误信息
        
        public static BreachCheckResult safe() {
            return BreachCheckResult.builder()
                .enabled(true)
                .breached(false)
                .breachCount(0)
                .hasError(false)
                .build();
        }
        
        public static BreachCheckResult breached(int count) {
            return BreachCheckResult.builder()
                .enabled(true)
                .breached(true)
                .breachCount(count)
                .hasError(false)
                .build();
        }
        
        public static BreachCheckResult error(String message) {
            return BreachCheckResult.builder()
                .enabled(true)
                .breached(false)
                .breachCount(0)
                .hasError(true)
                .errorMessage(message)
                .build();
        }
        
        public static BreachCheckResult disabled() {
            return BreachCheckResult.builder()
                .enabled(false)
                .breached(false)
                .breachCount(0)
                .hasError(false)
                .build();
        }
    }
}
