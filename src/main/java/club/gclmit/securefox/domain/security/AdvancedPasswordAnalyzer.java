package club.gclmit.securefox.domain.security;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 高级密码分析引擎
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@Service
public class AdvancedPasswordAnalyzer {
    
    // 常见弱密码字典
    private static final Set<String> COMMON_PASSWORDS = Set.of(
        "123456", "password", "123456789", "12345678", "12345", "1234567", "1234567890",
        "qwerty", "abc123", "111111", "123123", "admin", "letmein", "welcome", "monkey",
        "dragon", "pass", "master", "hello", "freedom", "whatever", "qazwsx", "trustno1"
    );
    
    // 键盘模式检测
    private static final Pattern[] KEYBOARD_PATTERNS = {
        Pattern.compile(".*qwerty.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*asdf.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*zxcv.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*1234.*"),
        Pattern.compile(".*abcd.*", Pattern.CASE_INSENSITIVE)
    };
    
    // 重复字符模式
    private static final Pattern REPEATING_CHARS = Pattern.compile("(.)\\1{2,}");
    
    // 连续字符模式
    private static final Pattern SEQUENTIAL_CHARS = Pattern.compile("(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|123|234|345|456|567|678|789)");
    
    /**
     * 综合密码强度分析
     * 
     * @param password 密码
     * @return 密码强度分析结果
     */
    public PasswordStrengthResult analyzePassword(String password) {
        if (password == null || password.isEmpty()) {
            return PasswordStrengthResult.builder()
                .score(0)
                .entropy(0.0)
                .weaknesses(List.of(WeaknessType.EMPTY_PASSWORD))
                .suggestions(List.of("密码不能为空"))
                .estimatedCrackTime("立即")
                .build();
        }
        
        int score = calculateScore(password);
        double entropy = calculateEntropy(password);
        List<WeaknessType> weaknesses = detectWeaknesses(password);
        List<String> suggestions = generateSuggestions(password, weaknesses);
        String crackTime = estimateCrackTime(entropy);
        
        return PasswordStrengthResult.builder()
            .score(score)
            .entropy(entropy)
            .weaknesses(weaknesses)
            .suggestions(suggestions)
            .estimatedCrackTime(crackTime)
            .build();
    }
    
    /**
     * 计算密码评分
     */
    private int calculateScore(String password) {
        int score = 0;
        
        // 长度评分
        if (password.length() >= 8) score += 1;
        if (password.length() >= 12) score += 1;
        if (password.length() >= 16) score += 1;
        if (password.length() >= 20) score += 1;
        
        // 字符类型评分
        if (password.matches(".*[a-z].*")) score += 1; // 小写字母
        if (password.matches(".*[A-Z].*")) score += 1; // 大写字母
        if (password.matches(".*\\d.*")) score += 1; // 数字
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) score += 1; // 特殊字符
        
        // 复杂度奖励
        if (hasGoodMixOfCharTypes(password)) score += 1;
        if (password.length() >= 16 && hasGoodMixOfCharTypes(password)) score += 1;
        
        // 弱点惩罚
        List<WeaknessType> weaknesses = detectWeaknesses(password);
        for (WeaknessType weakness : weaknesses) {
            switch (weakness) {
                case COMMON_PASSWORD -> score -= 3;
                case KEYBOARD_PATTERN -> score -= 2;
                case REPEATING_CHARS -> score -= 1;
                case SEQUENTIAL_CHARS -> score -= 1;
                case DICTIONARY_WORD -> score -= 1;
            }
        }
        
        return Math.max(0, Math.min(score, 10));
    }
    
    /**
     * 计算密码熵值
     */
    private double calculateEntropy(String password) {
        if (password.isEmpty()) return 0.0;
        
        // 计算字符集大小
        int charsetSize = 0;
        if (password.matches(".*[a-z].*")) charsetSize += 26; // 小写字母
        if (password.matches(".*[A-Z].*")) charsetSize += 26; // 大写字母
        if (password.matches(".*\\d.*")) charsetSize += 10; // 数字
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) charsetSize += 32; // 特殊字符
        
        if (charsetSize == 0) return 0.0;
        
        // 熵 = log2(字符集大小) * 密码长度
        double entropy = Math.log(charsetSize) / Math.log(2) * password.length();
        
        // 考虑重复字符的影响
        Map<Character, Integer> charFreq = new HashMap<>();
        for (char c : password.toCharArray()) {
            charFreq.put(c, charFreq.getOrDefault(c, 0) + 1);
        }
        
        // 使用香农熵公式调整
        double shannonEntropy = 0.0;
        for (int freq : charFreq.values()) {
            double probability = (double) freq / password.length();
            shannonEntropy -= probability * (Math.log(probability) / Math.log(2));
        }
        
        return Math.min(entropy, shannonEntropy * password.length());
    }
    
    /**
     * 检测密码弱点
     */
    private List<WeaknessType> detectWeaknesses(String password) {
        List<WeaknessType> weaknesses = new ArrayList<>();
        
        // 检查是否为常见密码
        if (isCommonPassword(password)) {
            weaknesses.add(WeaknessType.COMMON_PASSWORD);
        }
        
        // 检查键盘模式
        if (hasKeyboardPattern(password)) {
            weaknesses.add(WeaknessType.KEYBOARD_PATTERN);
        }
        
        // 检查重复字符
        if (hasRepeatingChars(password)) {
            weaknesses.add(WeaknessType.REPEATING_CHARS);
        }
        
        // 检查连续字符
        if (hasSequentialChars(password)) {
            weaknesses.add(WeaknessType.SEQUENTIAL_CHARS);
        }
        
        // 检查字典单词
        if (containsDictionaryWord(password)) {
            weaknesses.add(WeaknessType.DICTIONARY_WORD);
        }
        
        // 检查长度不足
        if (password.length() < 8) {
            weaknesses.add(WeaknessType.TOO_SHORT);
        }
        
        // 检查字符类型单一
        if (!hasGoodMixOfCharTypes(password)) {
            weaknesses.add(WeaknessType.LIMITED_CHAR_TYPES);
        }
        
        return weaknesses;
    }
    
    /**
     * 生成改进建议
     */
    private List<String> generateSuggestions(String password, List<WeaknessType> weaknesses) {
        List<String> suggestions = new ArrayList<>();
        
        for (WeaknessType weakness : weaknesses) {
            switch (weakness) {
                case TOO_SHORT -> suggestions.add("密码长度至少应为8位，建议12位以上");
                case COMMON_PASSWORD -> suggestions.add("避免使用常见密码，如'123456'、'password'等");
                case KEYBOARD_PATTERN -> suggestions.add("避免使用键盘上连续的字符，如'qwerty'、'asdf'等");
                case REPEATING_CHARS -> suggestions.add("避免使用重复字符，如'aaa'、'111'等");
                case SEQUENTIAL_CHARS -> suggestions.add("避免使用连续字符，如'abc'、'123'等");
                case DICTIONARY_WORD -> suggestions.add("避免使用完整的字典单词");
                case LIMITED_CHAR_TYPES -> suggestions.add("使用大小写字母、数字和特殊字符的组合");
                case EMPTY_PASSWORD -> suggestions.add("密码不能为空");
            }
        }
        
        if (suggestions.isEmpty()) {
            suggestions.add("密码强度良好，建议定期更换");
        }
        
        return suggestions.stream().distinct().collect(Collectors.toList());
    }
    
    /**
     * 估算破解时间
     */
    private String estimateCrackTime(double entropy) {
        if (entropy < 20) return "立即";
        if (entropy < 30) return "几秒钟";
        if (entropy < 40) return "几分钟";
        if (entropy < 50) return "几小时";
        if (entropy < 60) return "几天";
        if (entropy < 70) return "几个月";
        if (entropy < 80) return "几年";
        if (entropy < 90) return "几十年";
        return "几个世纪";
    }
    
    // 辅助方法
    private boolean isCommonPassword(String password) {
        return COMMON_PASSWORDS.contains(password.toLowerCase());
    }
    
    private boolean hasKeyboardPattern(String password) {
        String lowerPassword = password.toLowerCase();
        return Arrays.stream(KEYBOARD_PATTERNS)
            .anyMatch(pattern -> pattern.matcher(lowerPassword).find());
    }
    
    private boolean hasRepeatingChars(String password) {
        return REPEATING_CHARS.matcher(password).find();
    }
    
    private boolean hasSequentialChars(String password) {
        return SEQUENTIAL_CHARS.matcher(password.toLowerCase()).find();
    }
    
    private boolean containsDictionaryWord(String password) {
        // 简化的字典检查，实际应用中可以使用更完整的字典
        String[] commonWords = {"password", "admin", "user", "login", "welcome", "hello", "world"};
        String lowerPassword = password.toLowerCase();
        return Arrays.stream(commonWords)
            .anyMatch(lowerPassword::contains);
    }
    
    private boolean hasGoodMixOfCharTypes(String password) {
        int typeCount = 0;
        if (password.matches(".*[a-z].*")) typeCount++;
        if (password.matches(".*[A-Z].*")) typeCount++;
        if (password.matches(".*\\d.*")) typeCount++;
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) typeCount++;
        return typeCount >= 3;
    }
    
    /**
     * 密码强度分析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PasswordStrengthResult {
        private int score; // 0-10分
        private double entropy; // 熵值
        private List<WeaknessType> weaknesses; // 弱点列表
        private List<String> suggestions; // 改进建议
        private String estimatedCrackTime; // 估算破解时间
    }
    
    /**
     * 密码弱点类型
     */
    public enum WeaknessType {
        EMPTY_PASSWORD("密码为空"),
        TOO_SHORT("密码过短"),
        COMMON_PASSWORD("常见密码"),
        KEYBOARD_PATTERN("键盘模式"),
        REPEATING_CHARS("重复字符"),
        SEQUENTIAL_CHARS("连续字符"),
        DICTIONARY_WORD("字典单词"),
        LIMITED_CHAR_TYPES("字符类型单一");
        
        private final String description;
        
        WeaknessType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
