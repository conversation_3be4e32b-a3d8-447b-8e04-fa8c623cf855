package club.gclmit.securefox.domain.audit;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 审计日志实体
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@TableName("audit_logs")
public class AuditLog {
    
    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    private String action;
    
    /**
     * 资源类型
     */
    @NotBlank(message = "资源类型不能为空")
    private String resourceType;
    
    /**
     * 资源ID
     */
    private Long resourceId;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 操作详情（JSON格式）
     */
    private String details;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    // ==================== 工厂方法 ====================
    
    /**
     * 创建用户操作日志
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 审计日志实例
     */
    public static AuditLog create(Long userId, String action, String resourceType, 
                                 Long resourceId, String ipAddress, String userAgent) {
        AuditLog log = new AuditLog();
        log.setUserId(userId);
        log.setAction(action);
        log.setResourceType(resourceType);
        log.setResourceId(resourceId);
        log.setIpAddress(ipAddress);
        log.setUserAgent(userAgent);
        return log;
    }
    
    /**
     * 创建带详情的审计日志
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param details 操作详情
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 审计日志实例
     */
    public static AuditLog createWithDetails(Long userId, String action, String resourceType,
                                           Long resourceId, String details, String ipAddress, 
                                           String userAgent) {
        AuditLog log = create(userId, action, resourceType, resourceId, ipAddress, userAgent);
        log.setDetails(details);
        return log;
    }
    
    // ==================== 常用操作类型常量 ====================
    
    public static final class Actions {
        public static final String LOGIN = "LOGIN";
        public static final String LOGOUT = "LOGOUT";
        public static final String REGISTER = "REGISTER";
        public static final String CHANGE_PASSWORD = "CHANGE_PASSWORD";
        
        public static final String CREATE_PASSWORD = "CREATE_PASSWORD";
        public static final String UPDATE_PASSWORD = "UPDATE_PASSWORD";
        public static final String DELETE_PASSWORD = "DELETE_PASSWORD";
        public static final String VIEW_PASSWORD = "VIEW_PASSWORD";
        
        public static final String ENABLE_TOTP = "ENABLE_TOTP";
        public static final String DISABLE_TOTP = "DISABLE_TOTP";
        public static final String GENERATE_TOTP = "GENERATE_TOTP";
        
        public static final String CREATE_KEY = "CREATE_KEY";
        public static final String UPDATE_KEY = "UPDATE_KEY";
        public static final String DELETE_KEY = "DELETE_KEY";
        public static final String USE_KEY = "USE_KEY";
        
        public static final String CREATE_CATEGORY = "CREATE_CATEGORY";
        public static final String UPDATE_CATEGORY = "UPDATE_CATEGORY";
        public static final String DELETE_CATEGORY = "DELETE_CATEGORY";
        
        public static final String EXPORT_DATA = "EXPORT_DATA";
        public static final String IMPORT_DATA = "IMPORT_DATA";
        public static final String SYNC_DATA = "SYNC_DATA";
    }
    
    public static final class ResourceTypes {
        public static final String USER = "USER";
        public static final String PASSWORD = "PASSWORD";
        public static final String KEY = "KEY";
        public static final String CATEGORY = "CATEGORY";
        public static final String TOTP = "TOTP";
        public static final String SYSTEM = "SYSTEM";
    }
    
    @Override
    public String toString() {
        return "AuditLog{" +
                "id=" + id +
                ", userId=" + userId +
                ", action='" + action + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", resourceId=" + resourceId +
                ", ipAddress='" + ipAddress + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
