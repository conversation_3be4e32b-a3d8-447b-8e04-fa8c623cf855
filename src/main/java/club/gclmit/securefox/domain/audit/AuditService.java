package club.gclmit.securefox.domain.audit;

import club.gclmit.securefox.domain.user.User;
import club.gclmit.securefox.domain.vault.Password;


import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计服务接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public interface AuditService {
    
    /**
     * 记录审计日志
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param details 操作详情
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void log(Long userId, String action, String resourceType, Long resourceId, 
             String details, String ipAddress, String userAgent);
    
    /**
     * 记录用户登录
     * 
     * @param user 用户
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void logUserLogin(User user, String ipAddress, String userAgent);
    
    /**
     * 记录用户注册
     * 
     * @param user 用户
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void logUserRegister(User user, String ipAddress, String userAgent);
    
    /**
     * 记录密码创建
     * 
     * @param password 密码
     */
    void logPasswordCreated(Password password);
    
    /**
     * 记录密码更新
     * 
     * @param password 密码
     */
    void logPasswordUpdated(Password password);
    
    /**
     * 记录密码删除
     * 
     * @param password 密码
     */
    void logPasswordDeleted(Password password);
    
    /**
     * 记录密码查看
     * 
     * @param password 密码
     */
    void logPasswordViewed(Password password);
    
    /**
     * 记录TOTP启用
     * 
     * @param password 密码
     */
    void logTotpEnabled(Password password);
    
    /**
     * 记录TOTP禁用
     * 
     * @param password 密码
     */
    void logTotpDisabled(Password password);
    
    /**
     * 记录TOTP验证码生成
     * 
     * @param password 密码
     */
    void logTotpCodeGenerated(Password password);
    
    // Key 相关方法已移除 - 待后续实现
    
    /**
     * 查询用户审计日志
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码
     * @param size 每页大小
     * @return 审计日志列表
     */
    List<AuditLog> getUserAuditLogs(Long userId, LocalDateTime startTime, 
                                   LocalDateTime endTime, int page, int size);
    
    /**
     * 查询资源审计日志
     * 
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param page 页码
     * @param size 每页大小
     * @return 审计日志列表
     */
    List<AuditLog> getResourceAuditLogs(String resourceType, Long resourceId, int page, int size);
    
    /**
     * 统计用户操作次数
     * 
     * @param userId 用户ID
     * @param action 操作类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    long countUserActions(Long userId, String action, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 清理过期审计日志
     * 
     * @param retentionDays 保留天数
     * @return 清理的日志数量
     */
    long cleanupExpiredLogs(int retentionDays);
}
