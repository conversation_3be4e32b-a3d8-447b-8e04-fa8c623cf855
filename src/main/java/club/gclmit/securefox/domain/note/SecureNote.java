package club.gclmit.securefox.domain.note;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import club.gclmit.securefox.domain.security.EncryptionService;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 安全笔记聚合根
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@TableName("secure_notes")
public class SecureNote {
    
    /**
     * 笔记ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 笔记标题
     */
    @NotBlank(message = "笔记标题不能为空")
    private String title;
    
    /**
     * 加密的笔记内容
     */
    @NotBlank(message = "笔记内容不能为空")
    private String encryptedContent;
    
    /**
     * 笔记类型
     */
    @NotNull(message = "笔记类型不能为空")
    private NoteType noteType;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 标签（JSON数组格式）
     */
    private String tags;
    
    /**
     * 是否收藏
     */
    private Boolean favorite = false;
    
    /**
     * 是否置顶
     */
    private Boolean pinned = false;
    
    // ==================== 同步相关字段 ====================
    
    /**
     * 同步版本号
     */
    private Integer syncVersion = 1;
    
    /**
     * 最后修改设备
     */
    private String lastModifiedDevice;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 逻辑删除标记
     */
    @TableLogic
    private Integer deleted;
    
    // ==================== 领域方法 ====================
    
    /**
     * 更新笔记内容
     * 
     * @param newContent 新内容
     * @param encryptionService 加密服务
     */
    public void updateContent(String newContent, EncryptionService encryptionService) {
        this.encryptedContent = encryptionService.encrypt(newContent);
        this.incrementSyncVersion();
    }
    
    /**
     * 解密笔记内容
     * 
     * @param encryptionService 加密服务
     * @return 明文内容
     */
    public String decryptContent(EncryptionService encryptionService) {
        return encryptionService.decrypt(this.encryptedContent);
    }
    
    /**
     * 设置收藏状态
     * 
     * @param favorite 是否收藏
     */
    public void setFavorite(Boolean favorite) {
        this.favorite = favorite != null ? favorite : false;
        this.incrementSyncVersion();
    }
    
    /**
     * 设置置顶状态
     * 
     * @param pinned 是否置顶
     */
    public void setPinned(Boolean pinned) {
        this.pinned = pinned != null ? pinned : false;
        this.incrementSyncVersion();
    }
    
    /**
     * 更新基本信息
     * 
     * @param title 标题
     * @param noteType 笔记类型
     * @param categoryId 分类ID
     */
    public void updateBasicInfo(String title, NoteType noteType, Long categoryId) {
        if (StringUtils.isNotBlank(title)) {
            this.title = title.trim();
        }
        if (noteType != null) {
            this.noteType = noteType;
        }
        this.categoryId = categoryId;
        this.incrementSyncVersion();
    }
    
    // ==================== 同步相关方法 ====================
    
    /**
     * 增加同步版本号
     */
    public void incrementSyncVersion() {
        this.syncVersion = (this.syncVersion == null ? 1 : this.syncVersion) + 1;
    }
    
    /**
     * 设置最后修改设备
     * 
     * @param deviceId 设备ID
     */
    public void setLastModifiedDevice(String deviceId) {
        this.lastModifiedDevice = deviceId;
        this.incrementSyncVersion();
    }
    
    // ==================== 业务规则验证 ====================
    
    /**
     * 检查笔记是否属于指定用户
     * 
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    public boolean belongsToUser(Long userId) {
        return this.userId != null && this.userId.equals(userId);
    }
    
    /**
     * 检查标题是否有效
     * 
     * @param title 标题
     * @return 是否有效
     */
    public static boolean isValidTitle(String title) {
        return StringUtils.isNotBlank(title) && title.trim().length() <= 200;
    }
    
    /**
     * 检查内容长度是否有效
     * 
     * @param content 内容
     * @return 是否有效
     */
    public static boolean isValidContent(String content) {
        return StringUtils.isNotBlank(content) && content.length() <= 100000; // 100KB限制
    }
    
    /**
     * 获取内容预览
     * 
     * @param encryptionService 加密服务
     * @param maxLength 最大长度
     * @return 内容预览
     */
    public String getContentPreview(EncryptionService encryptionService, int maxLength) {
        String content = decryptContent(encryptionService);
        if (content.length() <= maxLength) {
            return content;
        }
        return content.substring(0, maxLength) + "...";
    }
    
    /**
     * 获取内容字数统计
     * 
     * @param encryptionService 加密服务
     * @return 字数
     */
    public int getContentLength(EncryptionService encryptionService) {
        String content = decryptContent(encryptionService);
        return content.length();
    }
    
    // ==================== 工厂方法 ====================
    
    /**
     * 创建新安全笔记
     * 
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param noteType 笔记类型
     * @param categoryId 分类ID
     * @param encryptionService 加密服务
     * @return 安全笔记实例
     */
    public static SecureNote create(Long userId, String title, String content, NoteType noteType,
                                   Long categoryId, EncryptionService encryptionService) {
        if (!isValidTitle(title)) {
            throw new IllegalArgumentException("笔记标题无效");
        }
        
        if (!isValidContent(content)) {
            throw new IllegalArgumentException("笔记内容无效或过长");
        }
        
        SecureNote note = new SecureNote();
        note.setUserId(userId);
        note.setTitle(title.trim());
        note.setNoteType(noteType);
        note.setCategoryId(categoryId);
        
        // 加密内容
        note.setEncryptedContent(encryptionService.encrypt(content));
        
        return note;
    }
    
    @Override
    public String toString() {
        return "SecureNote{" +
                "id=" + id +
                ", userId=" + userId +
                ", title='" + title + '\'' +
                ", noteType=" + noteType +
                ", categoryId=" + categoryId +
                ", favorite=" + favorite +
                ", pinned=" + pinned +
                ", syncVersion=" + syncVersion +
                ", createdAt=" + createdAt +
                '}';
    }
}
