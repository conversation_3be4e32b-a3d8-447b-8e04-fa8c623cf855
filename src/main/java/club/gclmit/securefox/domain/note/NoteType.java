package club.gclmit.securefox.domain.note;

/**
 * 安全笔记类型枚举
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public enum NoteType {
    
    /**
     * 普通笔记
     */
    GENERAL("普通笔记"),
    
    /**
     * 个人信息
     */
    PERSONAL_INFO("个人信息"),
    
    /**
     * 银行卡信息
     */
    BANK_CARD("银行卡信息"),
    
    /**
     * 身份证件
     */
    ID_DOCUMENT("身份证件"),
    
    /**
     * 软件许可证
     */
    SOFTWARE_LICENSE("软件许可证"),
    
    /**
     * 服务器信息
     */
    SERVER_INFO("服务器信息"),
    
    /**
     * 网络配置
     */
    NETWORK_CONFIG("网络配置"),
    
    /**
     * 重要文档
     */
    IMPORTANT_DOCUMENT("重要文档"),
    
    /**
     * 备份恢复码
     */
    RECOVERY_CODE("备份恢复码"),
    
    /**
     * 安全问题答案
     */
    SECURITY_QUESTION("安全问题答案"),
    
    /**
     * 加密密钥
     */
    ENCRYPTION_KEY("加密密钥"),
    
    /**
     * 其他敏感信息
     */
    OTHER_SENSITIVE("其他敏感信息");
    
    private final String description;
    
    NoteType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据描述获取笔记类型
     * 
     * @param description 描述
     * @return 笔记类型
     */
    public static NoteType fromDescription(String description) {
        for (NoteType type : values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        return GENERAL;
    }
    
    /**
     * 检查是否为高敏感类型
     * 
     * @return 是否高敏感
     */
    public boolean isHighSensitive() {
        return this == BANK_CARD || 
               this == ID_DOCUMENT || 
               this == ENCRYPTION_KEY ||
               this == RECOVERY_CODE ||
               this == SECURITY_QUESTION;
    }
    
    /**
     * 检查是否为技术类型
     * 
     * @return 是否技术类型
     */
    public boolean isTechnical() {
        return this == SERVER_INFO || 
               this == NETWORK_CONFIG || 
               this == SOFTWARE_LICENSE ||
               this == ENCRYPTION_KEY;
    }
    
    /**
     * 获取图标名称
     * 
     * @return 图标名称
     */
    public String getIconName() {
        switch (this) {
            case GENERAL:
                return "note";
            case PERSONAL_INFO:
                return "user";
            case BANK_CARD:
                return "credit-card";
            case ID_DOCUMENT:
                return "id-card";
            case SOFTWARE_LICENSE:
                return "key";
            case SERVER_INFO:
                return "server";
            case NETWORK_CONFIG:
                return "network";
            case IMPORTANT_DOCUMENT:
                return "file-text";
            case RECOVERY_CODE:
                return "shield";
            case SECURITY_QUESTION:
                return "help-circle";
            case ENCRYPTION_KEY:
                return "lock";
            case OTHER_SENSITIVE:
                return "eye-off";
            default:
                return "note";
        }
    }
    
    /**
     * 获取颜色代码
     * 
     * @return 颜色代码
     */
    public String getColorCode() {
        switch (this) {
            case GENERAL:
                return "#6C757D";
            case PERSONAL_INFO:
                return "#007BFF";
            case BANK_CARD:
                return "#28A745";
            case ID_DOCUMENT:
                return "#FFC107";
            case SOFTWARE_LICENSE:
                return "#17A2B8";
            case SERVER_INFO:
                return "#6F42C1";
            case NETWORK_CONFIG:
                return "#20C997";
            case IMPORTANT_DOCUMENT:
                return "#FD7E14";
            case RECOVERY_CODE:
                return "#DC3545";
            case SECURITY_QUESTION:
                return "#E83E8C";
            case ENCRYPTION_KEY:
                return "#343A40";
            case OTHER_SENSITIVE:
                return "#6C757D";
            default:
                return "#6C757D";
        }
    }
}
