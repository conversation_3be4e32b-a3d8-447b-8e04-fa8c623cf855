package club.gclmit.securefox.domain.note;

import java.util.List;
import java.util.Optional;

/**
 * 安全笔记仓储接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public interface SecureNoteRepository {
    
    /**
     * 保存安全笔记
     * 
     * @param note 安全笔记实体
     * @return 保存后的安全笔记
     */
    SecureNote save(SecureNote note);
    
    /**
     * 根据ID查找安全笔记
     * 
     * @param id 笔记ID
     * @return 安全笔记实体
     */
    Optional<SecureNote> findById(Long id);
    
    /**
     * 根据ID和用户ID查找安全笔记
     * 
     * @param id 笔记ID
     * @param userId 用户ID
     * @return 安全笔记实体
     */
    Optional<SecureNote> findByIdAndUserId(Long id, Long userId);
    
    /**
     * 根据用户ID查找所有安全笔记
     * 
     * @param userId 用户ID
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserId(Long userId);
    
    /**
     * 根据用户ID和笔记类型查找安全笔记
     * 
     * @param userId 用户ID
     * @param noteType 笔记类型
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserIdAndNoteType(Long userId, NoteType noteType);
    
    /**
     * 根据用户ID和分类ID查找安全笔记
     * 
     * @param userId 用户ID
     * @param categoryId 分类ID
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserIdAndCategoryId(Long userId, Long categoryId);
    
    /**
     * 搜索安全笔记
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 安全笔记列表
     */
    List<SecureNote> searchByKeyword(Long userId, String keyword);
    
    /**
     * 分页查询用户安全笔记
     * 
     * @param userId 用户ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserIdWithPagination(Long userId, int page, int size);
    
    /**
     * 查找收藏的安全笔记
     * 
     * @param userId 用户ID
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserIdAndFavorite(Long userId);
    
    /**
     * 查找置顶的安全笔记
     * 
     * @param userId 用户ID
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserIdAndPinned(Long userId);
    
    /**
     * 统计用户安全笔记数量
     * 
     * @param userId 用户ID
     * @return 安全笔记数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计指定类型的安全笔记数量
     * 
     * @param userId 用户ID
     * @param noteType 笔记类型
     * @return 安全笔记数量
     */
    long countByUserIdAndNoteType(Long userId, NoteType noteType);
    
    /**
     * 统计分类下的安全笔记数量
     * 
     * @param userId 用户ID
     * @param categoryId 分类ID
     * @return 安全笔记数量
     */
    long countByUserIdAndCategoryId(Long userId, Long categoryId);
    
    /**
     * 根据同步版本查找变更的安全笔记
     * 
     * @param userId 用户ID
     * @param sinceVersion 起始版本号
     * @return 安全笔记列表
     */
    List<SecureNote> findByUserIdAndSyncVersionGreaterThan(Long userId, Integer sinceVersion);
    
    /**
     * 删除安全笔记
     * 
     * @param id 笔记ID
     */
    void deleteById(Long id);
    
    /**
     * 批量删除用户的所有安全笔记
     * 
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
}
