package club.gclmit.securefox.domain.vault;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import club.gclmit.securefox.domain.security.EncryptionService;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 密码聚合根
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@TableName("passwords")
public class Password {
    
    /**
     * 密码ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 加密的密码
     */
    @NotBlank(message = "密码不能为空")
    private String encryptedPassword;
    
    /**
     * 网站地址
     */
    private String website;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    // ==================== TOTP相关字段 ====================
    
    /**
     * 加密的TOTP密钥
     */
    private String totpSecret;
    
    /**
     * TOTP发行者
     */
    private String totpIssuer;
    
    /**
     * TOTP账户名
     */
    private String totpAccountName;
    
    /**
     * TOTP验证码位数
     */
    private Integer totpDigits = 6;
    
    /**
     * TOTP时间间隔（秒）
     */
    private Integer totpPeriod = 30;
    
    /**
     * TOTP算法
     */
    private String totpAlgorithm = "SHA1";
    
    // ==================== 同步相关字段 ====================
    
    /**
     * 同步版本号
     */
    private Integer syncVersion = 1;
    
    /**
     * 最后修改设备
     */
    private String lastModifiedDevice;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 逻辑删除标记
     */
    @TableLogic
    private Integer deleted;
    
    // ==================== 密码相关领域方法 ====================
    
    /**
     * 更新密码
     * 
     * @param newPassword 新密码
     * @param encryptionService 加密服务
     */
    public void updatePassword(String newPassword, EncryptionService encryptionService) {
        this.encryptedPassword = encryptionService.encrypt(newPassword);
        this.incrementSyncVersion();
    }
    
    /**
     * 解密密码
     * 
     * @param encryptionService 加密服务
     * @return 明文密码
     */
    public String decryptPassword(EncryptionService encryptionService) {
        return encryptionService.decrypt(this.encryptedPassword);
    }
    
    // ==================== TOTP相关领域方法 ====================
    
    /**
     * 检查是否启用了TOTP
     * 
     * @return 是否启用TOTP
     */
    public boolean hasTotpEnabled() {
        return StringUtils.isNotBlank(this.totpSecret);
    }
    
    /**
     * 启用TOTP
     * 
     * @param totpConfig TOTP配置
     * @param encryptionService 加密服务
     */
    public void enableTotp(TotpConfig totpConfig, EncryptionService encryptionService) {
        this.totpSecret = encryptionService.encrypt(totpConfig.getSecret());
        this.totpIssuer = totpConfig.getIssuer();
        this.totpAccountName = totpConfig.getAccountName();
        this.totpDigits = totpConfig.getDigits();
        this.totpPeriod = totpConfig.getPeriod();
        this.totpAlgorithm = totpConfig.getAlgorithm();
        this.incrementSyncVersion();
    }
    
    /**
     * 禁用TOTP
     */
    public void disableTotp() {
        this.totpSecret = null;
        this.totpIssuer = null;
        this.totpAccountName = null;
        this.totpDigits = 6;
        this.totpPeriod = 30;
        this.totpAlgorithm = "SHA1";
        this.incrementSyncVersion();
    }
    
    /**
     * 获取TOTP配置
     * 
     * @param encryptionService 加密服务
     * @return TOTP配置
     */
    public TotpConfig getTotpConfig(EncryptionService encryptionService) {
        if (!hasTotpEnabled()) {
            return null;
        }
        
        return TotpConfig.builder()
                .secret(encryptionService.decrypt(this.totpSecret))
                .issuer(this.totpIssuer)
                .accountName(this.totpAccountName)
                .digits(this.totpDigits)
                .period(this.totpPeriod)
                .algorithm(this.totpAlgorithm)
                .build();
    }
    
    // ==================== 同步相关领域方法 ====================
    
    /**
     * 增加同步版本号
     */
    public void incrementSyncVersion() {
        this.syncVersion = (this.syncVersion == null ? 1 : this.syncVersion) + 1;
    }
    
    /**
     * 设置最后修改设备
     * 
     * @param deviceId 设备ID
     */
    public void setLastModifiedDevice(String deviceId) {
        this.lastModifiedDevice = deviceId;
        this.incrementSyncVersion();
    }
    
    // ==================== 业务规则验证 ====================
    
    /**
     * 检查密码是否属于指定用户
     * 
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    public boolean belongsToUser(Long userId) {
        return this.userId != null && this.userId.equals(userId);
    }
    
    /**
     * 检查网站URL格式
     * 
     * @param website 网站地址
     * @return 是否有效
     */
    public static boolean isValidWebsite(String website) {
        if (StringUtils.isBlank(website)) {
            return true; // 网站地址可以为空
        }
        
        // 简单的URL格式验证
        return website.matches("^https?://.*") || website.matches("^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}.*");
    }
    
    // ==================== 工厂方法 ====================
    
    /**
     * 创建新密码
     * 
     * @param userId 用户ID
     * @param title 标题
     * @param username 用户名
     * @param password 密码
     * @param website 网站
     * @param categoryId 分类ID
     * @param encryptionService 加密服务
     * @return 密码实例
     */
    public static Password create(Long userId, String title, String username, String password,
                                 String website, Long categoryId, EncryptionService encryptionService) {
        Password passwordEntity = new Password();
        passwordEntity.setUserId(userId);
        passwordEntity.setTitle(title);
        passwordEntity.setUsername(username);
        passwordEntity.setWebsite(website);
        passwordEntity.setCategoryId(categoryId);
        
        // 加密密码
        passwordEntity.setEncryptedPassword(encryptionService.encrypt(password));
        
        return passwordEntity;
    }
    
    @Override
    public String toString() {
        return "Password{" +
                "id=" + id +
                ", userId=" + userId +
                ", title='" + title + '\'' +
                ", username='" + username + '\'' +
                ", website='" + website + '\'' +
                ", hasTotpEnabled=" + hasTotpEnabled() +
                ", syncVersion=" + syncVersion +
                ", createdAt=" + createdAt +
                '}';
    }
}
