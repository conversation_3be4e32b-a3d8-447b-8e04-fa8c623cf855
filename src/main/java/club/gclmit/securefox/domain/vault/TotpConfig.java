package club.gclmit.securefox.domain.vault;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * TOTP配置值对象
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TotpConfig {
    
    /**
     * TOTP密钥
     */
    private String secret;
    
    /**
     * 发行者（如GitHub、Google等）
     */
    private String issuer;
    
    /**
     * 账户名
     */
    private String accountName;
    
    /**
     * 验证码位数（默认6位）
     */
    @Builder.Default
    private Integer digits = 6;
    
    /**
     * 时间间隔（秒，默认30秒）
     */
    @Builder.Default
    private Integer period = 30;
    
    /**
     * 算法（默认SHA1）
     */
    @Builder.Default
    private String algorithm = "SHA1";
    
    /**
     * 生成OTP Auth URL
     * 格式: otpauth://totp/Issuer:AccountName?secret=SECRET&issuer=ISSUER&digits=6&period=30&algorithm=SHA1
     * 
     * @return OTP Auth URL
     */
    public String generateOtpAuthUrl() {
        if (StringUtils.isBlank(secret)) {
            throw new IllegalStateException("TOTP密钥不能为空");
        }
        
        StringBuilder url = new StringBuilder("otpauth://totp/");
        
        // 添加标签 (Issuer:AccountName)
        if (StringUtils.isNotBlank(issuer)) {
            url.append(URLEncoder.encode(issuer, StandardCharsets.UTF_8));
            if (StringUtils.isNotBlank(accountName)) {
                url.append(":");
            }
        }
        if (StringUtils.isNotBlank(accountName)) {
            url.append(URLEncoder.encode(accountName, StandardCharsets.UTF_8));
        }
        
        // 添加参数
        url.append("?secret=").append(secret);
        
        if (StringUtils.isNotBlank(issuer)) {
            url.append("&issuer=").append(URLEncoder.encode(issuer, StandardCharsets.UTF_8));
        }
        
        if (digits != null && digits != 6) {
            url.append("&digits=").append(digits);
        }
        
        if (period != null && period != 30) {
            url.append("&period=").append(period);
        }
        
        if (StringUtils.isNotBlank(algorithm) && !"SHA1".equals(algorithm)) {
            url.append("&algorithm=").append(algorithm);
        }
        
        return url.toString();
    }
    
    /**
     * 从OTP Auth URL解析TOTP配置
     * 
     * @param url OTP Auth URL
     * @return TOTP配置
     */
    public static TotpConfig fromOtpAuthUrl(String url) {
        if (StringUtils.isBlank(url) || !url.startsWith("otpauth://totp/")) {
            throw new IllegalArgumentException("无效的OTP Auth URL");
        }
        
        try {
            // 解析URL
            String[] parts = url.split("\\?", 2);
            if (parts.length != 2) {
                throw new IllegalArgumentException("URL格式错误");
            }
            
            // 解析标签部分
            String labelPart = parts[0].substring("otpauth://totp/".length());
            String issuer = null;
            String accountName = null;
            
            if (labelPart.contains(":")) {
                String[] labelParts = labelPart.split(":", 2);
                issuer = URLDecoder.decode(labelParts[0], StandardCharsets.UTF_8);
                accountName = URLDecoder.decode(labelParts[1], StandardCharsets.UTF_8);
            } else {
                accountName = URLDecoder.decode(labelPart, StandardCharsets.UTF_8);
            }
            
            // 解析参数部分
            Map<String, String> params = parseQueryParams(parts[1]);
            
            String secret = params.get("secret");
            if (StringUtils.isBlank(secret)) {
                throw new IllegalArgumentException("缺少secret参数");
            }
            
            // 如果参数中有issuer，优先使用参数中的
            if (params.containsKey("issuer")) {
                issuer = params.get("issuer");
            }
            
            Integer digits = params.containsKey("digits") ? 
                Integer.parseInt(params.get("digits")) : 6;
            Integer period = params.containsKey("period") ? 
                Integer.parseInt(params.get("period")) : 30;
            String algorithm = params.getOrDefault("algorithm", "SHA1");
            
            return TotpConfig.builder()
                    .secret(secret)
                    .issuer(issuer)
                    .accountName(accountName)
                    .digits(digits)
                    .period(period)
                    .algorithm(algorithm)
                    .build();
                    
        } catch (Exception e) {
            throw new IllegalArgumentException("解析OTP Auth URL失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析查询参数
     * 
     * @param queryString 查询字符串
     * @return 参数映射
     */
    private static Map<String, String> parseQueryParams(String queryString) {
        Map<String, String> params = new HashMap<>();
        
        if (StringUtils.isBlank(queryString)) {
            return params;
        }
        
        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                String key = URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8);
                String value = URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8);
                params.put(key, value);
            }
        }
        
        return params;
    }
    
    /**
     * 验证TOTP配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return StringUtils.isNotBlank(secret) &&
               digits != null && digits >= 6 && digits <= 8 &&
               period != null && period >= 15 && period <= 300 &&
               StringUtils.isNotBlank(algorithm);
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (StringUtils.isNotBlank(issuer) && StringUtils.isNotBlank(accountName)) {
            return issuer + " (" + accountName + ")";
        } else if (StringUtils.isNotBlank(issuer)) {
            return issuer;
        } else if (StringUtils.isNotBlank(accountName)) {
            return accountName;
        } else {
            return "TOTP";
        }
    }
    
    @Override
    public String toString() {
        return "TotpConfig{" +
                "issuer='" + issuer + '\'' +
                ", accountName='" + accountName + '\'' +
                ", digits=" + digits +
                ", period=" + period +
                ", algorithm='" + algorithm + '\'' +
                '}';
    }
}
