package club.gclmit.securefox.domain.vault;

import club.gclmit.securefox.domain.security.AdvancedPasswordAnalyzer;
import club.gclmit.securefox.domain.security.EncryptionService;
import club.gclmit.securefox.domain.security.PasswordBreachChecker;
import club.gclmit.securefox.domain.security.SmartPasswordGenerator;
import club.gclmit.securefox.domain.security.TotpService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * 密码库领域服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
public class VaultDomainService {
    
    private final PasswordRepository passwordRepository;
    private final PasswordCategoryRepository categoryRepository;
    private final EncryptionService encryptionService;
    private final TotpService totpService;
    private final AdvancedPasswordAnalyzer passwordAnalyzer;
    private final PasswordBreachChecker breachChecker;
    private final SmartPasswordGenerator passwordGenerator;
    
    // 常见弱密码模式
    private static final Pattern[] WEAK_PASSWORD_PATTERNS = {
        Pattern.compile("^123456.*"),
        Pattern.compile("^password.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^qwerty.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^admin.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile("^.*123$"),
        Pattern.compile("^(.)\\1{2,}.*") // 连续相同字符
    };
    
    public VaultDomainService(PasswordRepository passwordRepository,
                             PasswordCategoryRepository categoryRepository,
                             EncryptionService encryptionService,
                             TotpService totpService,
                             AdvancedPasswordAnalyzer passwordAnalyzer,
                             PasswordBreachChecker breachChecker,
                             SmartPasswordGenerator passwordGenerator) {
        this.passwordRepository = passwordRepository;
        this.categoryRepository = categoryRepository;
        this.encryptionService = encryptionService;
        this.totpService = totpService;
        this.passwordAnalyzer = passwordAnalyzer;
        this.breachChecker = breachChecker;
        this.passwordGenerator = passwordGenerator;
    }
    
    /**
     * 创建密码
     * 
     * @param userId 用户ID
     * @param title 标题
     * @param username 用户名
     * @param password 密码
     * @param website 网站
     * @param categoryId 分类ID
     * @param notes 备注
     * @return 创建的密码实体
     */
    public Password createPassword(Long userId, String title, String username, String password,
                                  String website, Long categoryId, String notes) {
        // 验证分类是否属于用户
        if (categoryId != null) {
            validateCategoryBelongsToUser(categoryId, userId);
        }
        
        // 创建密码实体
        Password passwordEntity = Password.create(userId, title, username, password, 
                                                 website, categoryId, encryptionService);
        passwordEntity.setNotes(notes);
        
        return passwordRepository.save(passwordEntity);
    }
    
    /**
     * 为密码启用TOTP
     * 
     * @param passwordId 密码ID
     * @param userId 用户ID
     * @param totpConfig TOTP配置
     * @return 是否成功
     */
    public boolean enableTotpForPassword(Long passwordId, Long userId, TotpConfig totpConfig) {
        // 验证TOTP配置
        if (!totpConfig.isValid()) {
            throw new IllegalArgumentException("TOTP配置无效");
        }
        
        // 验证TOTP密钥
        if (!totpService.isValidSecret(totpConfig.getSecret())) {
            throw new IllegalArgumentException("TOTP密钥格式无效");
        }
        
        return passwordRepository.findByIdAndUserId(passwordId, userId)
                .map(password -> {
                    password.enableTotp(totpConfig, encryptionService);
                    passwordRepository.save(password);
                    return true;
                })
                .orElse(false);
    }
    
    /**
     * 生成TOTP验证码
     * 
     * @param passwordId 密码ID
     * @param userId 用户ID
     * @return TOTP验证码信息
     */
    public TotpCodeInfo generateTotpCode(Long passwordId, Long userId) {
        return passwordRepository.findByIdAndUserId(passwordId, userId)
                .filter(Password::hasTotpEnabled)
                .map(password -> {
                    TotpConfig config = password.getTotpConfig(encryptionService);
                    String code = totpService.generateCode(config);
                    int remainingSeconds = totpService.getRemainingSeconds(config);
                    
                    return new TotpCodeInfo(code, remainingSeconds, config.getIssuer(), 
                                          config.getAccountName());
                })
                .orElse(null);
    }
    
    /**
     * 检查密码强度（简化版本，兼容现有API）
     *
     * @param password 密码
     * @return 密码强度等级 (0-5)
     */
    public int checkPasswordStrength(String password) {
        var result = passwordAnalyzer.analyzePassword(password);
        // 将0-10的评分转换为0-5
        return Math.min(result.getScore() / 2, 5);
    }

    /**
     * 高级密码强度分析
     *
     * @param password 密码
     * @return 详细的密码强度分析结果
     */
    public AdvancedPasswordAnalyzer.PasswordStrengthResult analyzePasswordStrength(String password) {
        return passwordAnalyzer.analyzePassword(password);
    }

    /**
     * 检查密码是否泄露
     *
     * @param password 密码
     * @return 泄露检查结果
     */
    public PasswordBreachChecker.BreachCheckResult checkPasswordBreach(String password) {
        return breachChecker.checkPasswordBreach(password);
    }
    
    /**
     * 生成安全密码（兼容现有API）
     *
     * @param length 密码长度
     * @param includeUppercase 包含大写字母
     * @param includeLowercase 包含小写字母
     * @param includeNumbers 包含数字
     * @param includeSymbols 包含特殊字符
     * @return 生成的密码
     */
    public String generateSecurePassword(int length, boolean includeUppercase,
                                       boolean includeLowercase, boolean includeNumbers,
                                       boolean includeSymbols) {
        var config = SmartPasswordGenerator.PasswordGenerationConfig.builder()
            .length(length)
            .includeUppercase(includeUppercase)
            .includeLowercase(includeLowercase)
            .includeNumbers(includeNumbers)
            .includeSymbols(includeSymbols)
            .build();

        return passwordGenerator.generatePassword(config);
    }

    /**
     * 生成高安全性密码
     *
     * @param length 密码长度
     * @return 生成的高安全性密码
     */
    public String generateHighSecurityPassword(int length) {
        var config = SmartPasswordGenerator.PasswordGenerationConfig.getHighSecurity();
        config.setLength(length);
        return passwordGenerator.generatePassword(config);
    }

    /**
     * 生成用户友好密码
     *
     * @param length 密码长度
     * @return 生成的用户友好密码
     */
    public String generateUserFriendlyPassword(int length) {
        var config = SmartPasswordGenerator.PasswordGenerationConfig.getUserFriendly();
        config.setLength(length);
        return passwordGenerator.generatePassword(config);
    }

    /**
     * 生成记忆友好密码
     *
     * @param wordCount 单词数量
     * @param separator 分隔符
     * @param includeNumbers 是否包含数字
     * @return 生成的记忆友好密码
     */
    public String generateMemorablePassword(int wordCount, String separator, boolean includeNumbers) {
        return passwordGenerator.generateMemorablePassword(wordCount, separator, includeNumbers, true);
    }
    
    /**
     * 创建默认分类
     * 
     * @param userId 用户ID
     * @return 创建的分类列表
     */
    public List<PasswordCategory> createDefaultCategories(Long userId) {
        PasswordCategory[] defaultCategories = PasswordCategory.getDefaultCategories(userId);
        return categoryRepository.saveAll(List.of(defaultCategories));
    }
    
    /**
     * 验证分类是否属于用户
     * 
     * @param categoryId 分类ID
     * @param userId 用户ID
     */
    private void validateCategoryBelongsToUser(Long categoryId, Long userId) {
        categoryRepository.findByIdAndUserId(categoryId, userId)
                .orElseThrow(() -> new IllegalArgumentException("分类不存在或不属于当前用户"));
    }
    
    /**
     * 搜索密码
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @return 匹配的密码列表
     */
    public List<Password> searchPasswords(Long userId, String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return passwordRepository.findByUserId(userId);
        }
        
        return passwordRepository.searchByKeyword(userId, keyword.trim());
    }
    
    /**
     * 检查重复密码
     * 
     * @param userId 用户ID
     * @param password 密码
     * @return 使用相同密码的密码实体列表
     */
    public List<Password> findDuplicatePasswords(Long userId, String password) {
        List<Password> userPasswords = passwordRepository.findByUserId(userId);
        
        return userPasswords.stream()
                .filter(p -> {
                    String decryptedPassword = p.decryptPassword(encryptionService);
                    return password.equals(decryptedPassword);
                })
                .toList();
    }
    
    /**
     * TOTP验证码信息
     */
    public static class TotpCodeInfo {
        private final String code;
        private final int remainingSeconds;
        private final String issuer;
        private final String accountName;
        
        public TotpCodeInfo(String code, int remainingSeconds, String issuer, String accountName) {
            this.code = code;
            this.remainingSeconds = remainingSeconds;
            this.issuer = issuer;
            this.accountName = accountName;
        }
        
        public String getCode() { return code; }
        public int getRemainingSeconds() { return remainingSeconds; }
        public String getIssuer() { return issuer; }
        public String getAccountName() { return accountName; }
    }
}
