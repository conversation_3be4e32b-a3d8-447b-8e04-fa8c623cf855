package club.gclmit.securefox.domain.vault;

import java.util.List;
import java.util.Optional;

/**
 * 密码分类仓储接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
public interface PasswordCategoryRepository {
    
    /**
     * 保存分类
     * 
     * @param category 分类实体
     * @return 保存后的分类
     */
    PasswordCategory save(PasswordCategory category);
    
    /**
     * 根据ID查找分类
     * 
     * @param id 分类ID
     * @return 分类实体
     */
    Optional<PasswordCategory> findById(Long id);
    
    /**
     * 根据ID和用户ID查找分类
     * 
     * @param id 分类ID
     * @param userId 用户ID
     * @return 分类实体
     */
    Optional<PasswordCategory> findByIdAndUserId(Long id, Long userId);
    
    /**
     * 根据用户ID查找所有分类
     * 
     * @param userId 用户ID
     * @return 分类列表
     */
    List<PasswordCategory> findByUserId(Long userId);
    
    /**
     * 根据用户ID和名称查找分类
     * 
     * @param userId 用户ID
     * @param name 分类名称
     * @return 分类实体
     */
    Optional<PasswordCategory> findByUserIdAndName(Long userId, String name);
    
    /**
     * 检查用户是否已存在同名分类
     * 
     * @param userId 用户ID
     * @param name 分类名称
     * @return 是否存在
     */
    boolean existsByUserIdAndName(Long userId, String name);
    
    /**
     * 统计用户分类数量
     * 
     * @param userId 用户ID
     * @return 分类数量
     */
    long countByUserId(Long userId);
    
    /**
     * 删除分类
     * 
     * @param id 分类ID
     */
    void deleteById(Long id);
    
    /**
     * 批量删除用户的所有分类
     * 
     * @param userId 用户ID
     */
    void deleteByUserId(Long userId);
    
    /**
     * 批量保存分类
     * 
     * @param categories 分类列表
     * @return 保存后的分类列表
     */
    List<PasswordCategory> saveAll(List<PasswordCategory> categories);
}
