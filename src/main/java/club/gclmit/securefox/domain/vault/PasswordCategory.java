package club.gclmit.securefox.domain.vault;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 密码分类实体
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@TableName("categories")
public class PasswordCategory {
    
    /**
     * 分类ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;
    
    /**
     * 分类颜色（十六进制颜色值）
     */
    private String color = "#007AFF";
    
    /**
     * 分类图标
     */
    private String icon = "folder";
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 逻辑删除标记
     */
    @TableLogic
    private Integer deleted;
    
    // ==================== 领域方法 ====================
    
    /**
     * 检查分类是否属于指定用户
     * 
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    public boolean belongsToUser(Long userId) {
        return this.userId != null && this.userId.equals(userId);
    }
    
    /**
     * 更新分类信息
     * 
     * @param name 新名称
     * @param color 新颜色
     * @param icon 新图标
     */
    public void updateInfo(String name, String color, String icon) {
        if (name != null && !name.trim().isEmpty()) {
            this.name = name.trim();
        }
        if (color != null && isValidColor(color)) {
            this.color = color;
        }
        if (icon != null && !icon.trim().isEmpty()) {
            this.icon = icon.trim();
        }
    }
    
    /**
     * 检查颜色值是否有效
     * 
     * @param color 颜色值
     * @return 是否有效
     */
    public static boolean isValidColor(String color) {
        if (color == null || color.trim().isEmpty()) {
            return false;
        }
        
        // 检查十六进制颜色格式 #RRGGBB
        return color.matches("^#[0-9A-Fa-f]{6}$");
    }
    
    /**
     * 检查图标名称是否有效
     * 
     * @param icon 图标名称
     * @return 是否有效
     */
    public static boolean isValidIcon(String icon) {
        if (icon == null || icon.trim().isEmpty()) {
            return false;
        }
        
        // 图标名称只能包含字母、数字、连字符和下划线
        return icon.matches("^[a-zA-Z0-9_-]+$");
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return this.name;
    }
    
    // ==================== 工厂方法 ====================
    
    /**
     * 创建新分类
     * 
     * @param userId 用户ID
     * @param name 分类名称
     * @param color 分类颜色
     * @param icon 分类图标
     * @return 分类实例
     */
    public static PasswordCategory create(Long userId, String name, String color, String icon) {
        PasswordCategory category = new PasswordCategory();
        category.setUserId(userId);
        category.setName(name.trim());
        
        if (color != null && isValidColor(color)) {
            category.setColor(color);
        }
        
        if (icon != null && isValidIcon(icon)) {
            category.setIcon(icon);
        }
        
        return category;
    }
    
    /**
     * 创建默认分类
     * 
     * @param userId 用户ID
     * @param name 分类名称
     * @return 分类实例
     */
    public static PasswordCategory createDefault(Long userId, String name) {
        return create(userId, name, "#007AFF", "folder");
    }
    
    // ==================== 预定义分类 ====================
    
    /**
     * 获取默认分类列表
     * 
     * @param userId 用户ID
     * @return 默认分类列表
     */
    public static PasswordCategory[] getDefaultCategories(Long userId) {
        return new PasswordCategory[]{
            create(userId, "工作", "#FF6B6B", "briefcase"),
            create(userId, "个人", "#4ECDC4", "user"),
            create(userId, "社交", "#45B7D1", "users"),
            create(userId, "金融", "#96CEB4", "credit-card"),
            create(userId, "购物", "#FFEAA7", "shopping-cart"),
            create(userId, "娱乐", "#DDA0DD", "gamepad"),
            create(userId, "教育", "#98D8C8", "graduation-cap"),
            create(userId, "其他", "#F7DC6F", "folder")
        };
    }
    
    @Override
    public String toString() {
        return "PasswordCategory{" +
                "id=" + id +
                ", userId=" + userId +
                ", name='" + name + '\'' +
                ", color='" + color + '\'' +
                ", icon='" + icon + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
