package club.gclmit.securefox.domain.user;

import club.gclmit.securefox.domain.security.EncryptionService;
import org.springframework.stereotype.Service;

/**
 * 用户领域服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Service
public class UserDomainService {
    
    private final UserRepository userRepository;
    private final EncryptionService encryptionService;
    
    public UserDomainService(UserRepository userRepository, EncryptionService encryptionService) {
        this.userRepository = userRepository;
        this.encryptionService = encryptionService;
    }
    
    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 是否可用
     */
    public boolean isUsernameAvailable(String username) {
        if (!User.isValidUsername(username)) {
            return false;
        }
        
        return !userRepository.existsByUsername(username);
    }
    
    /**
     * 检查邮箱是否可用
     * 
     * @param email 邮箱
     * @return 是否可用
     */
    public boolean isEmailAvailable(String email) {
        if (!User.isValidEmail(email)) {
            return false;
        }
        
        return !userRepository.existsByEmail(email);
    }
    
    /**
     * 验证用户登录
     * 
     * @param email 邮箱
     * @param password 密码
     * @return 用户实体，如果验证失败返回null
     */
    public User authenticateUser(String email, String password) {
        return userRepository.findByEmail(email)
                .filter(user -> user.verifyMasterPassword(password, encryptionService))
                .orElse(null);
    }
    
    /**
     * 创建新用户
     * 
     * @param username 用户名
     * @param email 邮箱
     * @param masterPassword 主密码
     * @return 创建的用户
     * @throws IllegalArgumentException 如果参数无效
     */
    public User createUser(String username, String email, String masterPassword) {
        // 验证用户名
        if (!isUsernameAvailable(username)) {
            throw new IllegalArgumentException("用户名不可用");
        }
        
        // 验证邮箱
        if (!isEmailAvailable(email)) {
            throw new IllegalArgumentException("邮箱不可用");
        }
        
        // 验证主密码强度
        if (!User.isValidMasterPassword(masterPassword)) {
            throw new IllegalArgumentException("主密码强度不符合要求");
        }
        
        // 创建用户
        User user = User.create(username, email, masterPassword, encryptionService);
        return userRepository.save(user);
    }
    
    /**
     * 更改用户主密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    public boolean changeMasterPassword(Long userId, String oldPassword, String newPassword) {
        return userRepository.findById(userId)
                .filter(user -> user.verifyMasterPassword(oldPassword, encryptionService))
                .map(user -> {
                    if (!User.isValidMasterPassword(newPassword)) {
                        throw new IllegalArgumentException("新密码强度不符合要求");
                    }
                    
                    user.changeMasterPassword(newPassword, encryptionService);
                    userRepository.save(user);
                    return true;
                })
                .orElse(false);
    }
    
    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 密码强度等级 (1-5)
     */
    public int checkPasswordStrength(String password) {
        if (password == null || password.isEmpty()) {
            return 0;
        }
        
        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        
        // 字符类型检查
        if (password.matches(".*[a-z].*")) score++; // 小写字母
        if (password.matches(".*[A-Z].*")) score++; // 大写字母
        if (password.matches(".*\\d.*")) score++; // 数字
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) score++; // 特殊字符
        
        // 复杂度检查
        if (password.length() >= 16) score++;
        
        return Math.min(score, 5);
    }
    
    /**
     * 生成密码强度描述
     * 
     * @param password 密码
     * @return 强度描述
     */
    public String getPasswordStrengthDescription(String password) {
        int strength = checkPasswordStrength(password);
        
        switch (strength) {
            case 0:
            case 1:
                return "非常弱";
            case 2:
                return "弱";
            case 3:
                return "中等";
            case 4:
                return "强";
            case 5:
                return "非常强";
            default:
                return "未知";
        }
    }
    
    /**
     * 验证用户是否存在且有效
     * 
     * @param userId 用户ID
     * @return 是否存在且有效
     */
    public boolean isUserValid(Long userId) {
        return userRepository.findById(userId)
                .map(User::isValid)
                .orElse(false);
    }
}
