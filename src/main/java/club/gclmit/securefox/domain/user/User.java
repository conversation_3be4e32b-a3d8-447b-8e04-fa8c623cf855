package club.gclmit.securefox.domain.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import club.gclmit.securefox.domain.security.EncryptionService;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户聚合根
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@TableName("users")
public class User {
    
    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;
    
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 主密码哈希值
     */
    @NotBlank
    private String masterPasswordHash;
    
    /**
     * 盐值
     */
    @NotBlank
    private String salt;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    /**
     * 逻辑删除标记
     */
    @TableLogic
    private Integer deleted;
    
    // ==================== 领域方法 ====================
    
    /**
     * 验证主密码
     * 
     * @param inputPassword 输入的密码
     * @param encryptionService 加密服务
     * @return 验证结果
     */
    public boolean verifyMasterPassword(String inputPassword, EncryptionService encryptionService) {
        return encryptionService.verifyPassword(inputPassword, this.masterPasswordHash, this.salt);
    }
    
    /**
     * 更改主密码
     * 
     * @param newPassword 新密码
     * @param encryptionService 加密服务
     */
    public void changeMasterPassword(String newPassword, EncryptionService encryptionService) {
        this.salt = encryptionService.generateSalt();
        this.masterPasswordHash = encryptionService.hashPassword(newPassword, this.salt);
    }
    
    /**
     * 检查用户是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return this.deleted == null || this.deleted == 0;
    }
    
    /**
     * 获取用户显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return this.username;
    }
    
    /**
     * 检查邮箱是否已验证
     * 
     * @return 是否已验证
     */
    public boolean isEmailVerified() {
        // TODO: 后续可以添加邮箱验证功能
        return true;
    }
    
    // ==================== 工厂方法 ====================
    
    /**
     * 创建新用户
     * 
     * @param username 用户名
     * @param email 邮箱
     * @param masterPassword 主密码
     * @param encryptionService 加密服务
     * @return 用户实例
     */
    public static User create(String username, String email, String masterPassword, 
                             EncryptionService encryptionService) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        
        // 生成盐值并哈希密码
        String salt = encryptionService.generateSalt();
        String hashedPassword = encryptionService.hashPassword(masterPassword, salt);
        
        user.setSalt(salt);
        user.setMasterPasswordHash(hashedPassword);
        
        return user;
    }
    
    // ==================== 业务规则验证 ====================
    
    /**
     * 验证用户名格式
     * 
     * @param username 用户名
     * @return 是否有效
     */
    public static boolean isValidUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        // 用户名只能包含字母、数字、下划线和连字符
        return username.matches("^[a-zA-Z0-9_-]{3,50}$");
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        // 简单的邮箱格式验证
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }
    
    /**
     * 验证主密码强度
     * 
     * @param password 密码
     * @return 是否符合要求
     */
    public static boolean isValidMasterPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        
        // 至少包含一个大写字母、一个小写字母、一个数字
        boolean hasUpper = password.matches(".*[A-Z].*");
        boolean hasLower = password.matches(".*[a-z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        
        return hasUpper && hasLower && hasDigit;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
