package club.gclmit.securefox.interfaces.controller;

import club.gclmit.securefox.application.service.ImportExportService;
import club.gclmit.securefox.interfaces.dto.ApiResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 导入导出控制器
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/import-export")
public class ImportExportController {
    
    private final ImportExportService importExportService;
    
    public ImportExportController(ImportExportService importExportService) {
        this.importExportService = importExportService;
    }
    
    /**
     * 导入Bitwarden数据
     */
    @PostMapping("/bitwarden/import")
    public ApiResponse<ImportExportService.ImportResult> importFromBitwarden(
            @RequestParam("file") MultipartFile file) {
        
        // 验证文件
        if (file.isEmpty()) {
            return ApiResponse.badRequestWithType("请选择要导入的文件");
        }

        if (!file.getOriginalFilename().toLowerCase().endsWith(".json")) {
            return ApiResponse.badRequestWithType("只支持JSON格式的Bitwarden导出文件");
        }

        if (file.getSize() > 10 * 1024 * 1024) { // 10MB限制
            return ApiResponse.badRequestWithType("文件大小不能超过10MB");
        }
        
        try {
            ImportExportService.ImportResult result = importExportService.importFromBitwarden(file);

            return ApiResponse.success(result);
        } catch (IOException e) {
            return ApiResponse.badRequestWithType("文件解析失败：" + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ApiResponse.badRequestWithType(e.getMessage());
        } catch (Exception e) {
            return ApiResponse.badRequestWithType("导入失败：" + e.getMessage());
        }
    }
    
    /**
     * 导出为Bitwarden格式
     */
    @GetMapping("/bitwarden/export")
    public ResponseEntity<?> exportToBitwarden() {
        try {
            String jsonData = importExportService.exportToBitwarden();
            
            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = "securefox_export_" + timestamp + ".json";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(jsonData);
                    
        } catch (IOException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("导出失败：" + e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("导出失败：" + e.getMessage()));
        }
    }
    
    /**
     * 获取导入导出说明
     */
    @GetMapping("/help")
    public ApiResponse<ImportExportHelp> getImportExportHelp() {
        ImportExportHelp help = new ImportExportHelp();
        return ApiResponse.success(help);
    }
    
    /**
     * 导入导出帮助信息
     */
    public static class ImportExportHelp {
        
        public String getImportInstructions() {
            return "1. 在Bitwarden中导出数据：设置 -> 工具 -> 导出密码库 -> 选择JSON格式（未加密）\n" +
                   "2. 上传导出的JSON文件到SecureFox\n" +
                   "3. 系统会自动导入登录项目和分类\n" +
                   "4. 不支持加密的导出文件，请选择未加密的JSON格式";
        }
        
        public String getExportInstructions() {
            return "1. 点击导出按钮下载JSON文件\n" +
                   "2. 文件包含所有密码、分类和TOTP信息\n" +
                   "3. 导出的文件可以导入到Bitwarden或其他兼容的密码管理器\n" +
                   "4. 请妥善保管导出文件，其中包含明文密码";
        }
        
        public String getSupportedFormats() {
            return "支持的格式：\n" +
                   "- 导入：Bitwarden JSON格式（未加密）\n" +
                   "- 导出：Bitwarden兼容的JSON格式";
        }
        
        public String getSecurityNotes() {
            return "安全提醒：\n" +
                   "1. 导出文件包含明文密码，请确保文件安全\n" +
                   "2. 导入完成后建议删除原始导出文件\n" +
                   "3. 不要在不安全的网络环境中传输导出文件\n" +
                   "4. 建议在导入前备份现有数据";
        }
        
        public String getLimitations() {
            return "当前限制：\n" +
                   "1. 只支持登录类型的项目导入\n" +
                   "2. 不支持安全笔记、卡片、身份信息\n" +
                   "3. 文件大小限制：10MB\n" +
                   "4. 不支持加密的Bitwarden导出文件";
        }
    }
}
