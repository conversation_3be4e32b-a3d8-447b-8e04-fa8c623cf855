package club.gclmit.securefox.interfaces.controller;

import club.gclmit.securefox.application.command.CreatePasswordCommand;
import club.gclmit.securefox.application.dto.PasswordDTO;
import club.gclmit.securefox.application.service.VaultApplicationService;
import club.gclmit.securefox.domain.security.AdvancedPasswordAnalyzer;
import club.gclmit.securefox.domain.security.PasswordBreachChecker;
import club.gclmit.securefox.domain.vault.TotpConfig;
import club.gclmit.securefox.interfaces.dto.ApiResponse;

import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 密码库管理控制器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/passwords")
public class VaultController {
    
    private final VaultApplicationService vaultApplicationService;
    
    public VaultController(VaultApplicationService vaultApplicationService) {
        this.vaultApplicationService = vaultApplicationService;
    }
    
    /**
     * 创建密码
     */
    @PostMapping
    public ApiResponse<PasswordDTO> createPassword(@Valid @RequestBody CreatePasswordCommand command) {
        PasswordDTO password = vaultApplicationService.createPassword(command);
        return ApiResponse.success("密码创建成功", password);
    }
    
    /**
     * 获取密码列表
     */
    @GetMapping
    public ApiResponse<List<PasswordDTO>> getPasswords() {
        List<PasswordDTO> passwords = vaultApplicationService.getUserPasswords();
        return ApiResponse.success(passwords);
    }
    
    /**
     * 获取密码详情
     */
    @GetMapping("/{id}")
    public ApiResponse<PasswordDTO> getPassword(@PathVariable Long id) {
        PasswordDTO password = vaultApplicationService.getPassword(id);
        return ApiResponse.success(password);
    }
    
    /**
     * 获取密码明文
     */
    @GetMapping("/{id}/plaintext")
    public ApiResponse<String> getPasswordPlaintext(@PathVariable Long id) {
        String plaintext = vaultApplicationService.getPasswordPlaintext(id);
        return ApiResponse.success(plaintext);
    }
    
    /**
     * 更新密码
     */
    @PutMapping("/{id}")
    public ApiResponse<PasswordDTO> updatePassword(@PathVariable Long id, 
                                                  @Valid @RequestBody CreatePasswordCommand command) {
        PasswordDTO password = vaultApplicationService.updatePassword(id, command);
        return ApiResponse.success("密码更新成功", password);
    }
    
    /**
     * 删除密码
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deletePassword(@PathVariable Long id) {
        vaultApplicationService.deletePassword(id);
        return ApiResponse.successMessage("密码删除成功");
    }
    
    /**
     * 搜索密码
     */
    @GetMapping("/search")
    public ApiResponse<List<PasswordDTO>> searchPasswords(@RequestParam String keyword) {
        List<PasswordDTO> passwords = vaultApplicationService.searchPasswords(keyword);
        return ApiResponse.success(passwords);
    }
    
    /**
     * 启用TOTP
     */
    @PostMapping("/{id}/totp")
    public ApiResponse<String> enableTotp(@PathVariable Long id, @RequestBody TotpConfig totpConfig) {
        boolean success = vaultApplicationService.enableTotp(id, totpConfig);
        if (success) {
            return ApiResponse.successMessage("TOTP启用成功");
        } else {
            return ApiResponse.badRequestWithType("TOTP启用失败");
        }
    }
    
    /**
     * 禁用TOTP
     */
    @DeleteMapping("/{id}/totp")
    public ApiResponse<String> disableTotp(@PathVariable Long id) {
        boolean success = vaultApplicationService.disableTotp(id);
        if (success) {
            return ApiResponse.successMessage("TOTP禁用成功");
        } else {
            return ApiResponse.badRequestWithType("TOTP禁用失败");
        }
    }
    
    /**
     * 生成TOTP验证码
     */
    @GetMapping("/{id}/totp/code")
    public ApiResponse<VaultApplicationService.TotpCodeInfo> generateTotpCode(@PathVariable Long id) {
        VaultApplicationService.TotpCodeInfo codeInfo = vaultApplicationService.generateTotpCode(id);
        if (codeInfo != null) {
            return ApiResponse.success(codeInfo);
        } else {
            return ApiResponse.badRequestWithType("该密码未启用TOTP");
        }
    }
    
    /**
     * 检查密码强度
     */
    @PostMapping("/strength")
    public ApiResponse<Integer> checkPasswordStrength(@RequestBody PasswordStrengthRequest request) {
        int strength = vaultApplicationService.checkPasswordStrength(request.getPassword());
        return ApiResponse.success(strength);
    }
    
    /**
     * 生成安全密码
     */
    @PostMapping("/generate")
    public ApiResponse<String> generateSecurePassword(@RequestBody GeneratePasswordRequest request) {
        String password = vaultApplicationService.generateSecurePassword(
            request.getLength(),
            request.isIncludeUppercase(),
            request.isIncludeLowercase(),
            request.isIncludeNumbers(),
            request.isIncludeSymbols()
        );
        return ApiResponse.success(password);
    }

    /**
     * 高级密码强度分析
     */
    @PostMapping("/strength/advanced")
    public ApiResponse<AdvancedPasswordAnalyzer.PasswordStrengthResult> analyzePasswordStrength(
            @RequestBody PasswordStrengthRequest request) {
        var result = vaultApplicationService.analyzePasswordStrength(request.getPassword());
        return ApiResponse.success(result);
    }

    /**
     * 检查密码是否泄露
     */
    @PostMapping("/breach-check")
    public ApiResponse<PasswordBreachChecker.BreachCheckResult> checkPasswordBreach(
            @RequestBody PasswordStrengthRequest request) {
        var result = vaultApplicationService.checkPasswordBreach(request.getPassword());
        return ApiResponse.success(result);
    }

    /**
     * 生成高安全性密码
     */
    @PostMapping("/generate/high-security")
    public ApiResponse<String> generateHighSecurityPassword(@RequestBody HighSecurityPasswordRequest request) {
        String password = vaultApplicationService.generateHighSecurityPassword(request.getLength());
        return ApiResponse.success(password);
    }

    /**
     * 生成用户友好密码
     */
    @PostMapping("/generate/user-friendly")
    public ApiResponse<String> generateUserFriendlyPassword(@RequestBody UserFriendlyPasswordRequest request) {
        String password = vaultApplicationService.generateUserFriendlyPassword(request.getLength());
        return ApiResponse.success(password);
    }

    /**
     * 生成记忆友好密码
     */
    @PostMapping("/generate/memorable")
    public ApiResponse<String> generateMemorablePassword(@RequestBody MemorablePasswordRequest request) {
        String password = vaultApplicationService.generateMemorablePassword(
            request.getWordCount(),
            request.getSeparator(),
            request.isIncludeNumbers()
        );
        return ApiResponse.success(password);
    }

    /**
     * 密码强度检查请求
     */
    public static class PasswordStrengthRequest {
        private String password;
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }
    
    /**
     * 生成密码请求
     */
    public static class GeneratePasswordRequest {
        private int length = 12;
        private boolean includeUppercase = true;
        private boolean includeLowercase = true;
        private boolean includeNumbers = true;
        private boolean includeSymbols = false;
        
        public int getLength() { return length; }
        public void setLength(int length) { this.length = length; }
        public boolean isIncludeUppercase() { return includeUppercase; }
        public void setIncludeUppercase(boolean includeUppercase) { this.includeUppercase = includeUppercase; }
        public boolean isIncludeLowercase() { return includeLowercase; }
        public void setIncludeLowercase(boolean includeLowercase) { this.includeLowercase = includeLowercase; }
        public boolean isIncludeNumbers() { return includeNumbers; }
        public void setIncludeNumbers(boolean includeNumbers) { this.includeNumbers = includeNumbers; }
        public boolean isIncludeSymbols() { return includeSymbols; }
        public void setIncludeSymbols(boolean includeSymbols) { this.includeSymbols = includeSymbols; }
    }

    /**
     * 高安全性密码生成请求
     */
    public static class HighSecurityPasswordRequest {
        private int length = 16;

        public int getLength() { return length; }
        public void setLength(int length) { this.length = length; }
    }

    /**
     * 用户友好密码生成请求
     */
    public static class UserFriendlyPasswordRequest {
        private int length = 12;

        public int getLength() { return length; }
        public void setLength(int length) { this.length = length; }
    }

    /**
     * 记忆友好密码生成请求
     */
    public static class MemorablePasswordRequest {
        private int wordCount = 3;
        private String separator = "-";
        private boolean includeNumbers = true;

        public int getWordCount() { return wordCount; }
        public void setWordCount(int wordCount) { this.wordCount = wordCount; }
        public String getSeparator() { return separator; }
        public void setSeparator(String separator) { this.separator = separator; }
        public boolean isIncludeNumbers() { return includeNumbers; }
        public void setIncludeNumbers(boolean includeNumbers) { this.includeNumbers = includeNumbers; }
    }
}
