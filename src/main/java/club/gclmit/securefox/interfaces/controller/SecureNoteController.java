package club.gclmit.securefox.interfaces.controller;

import club.gclmit.securefox.application.command.CreateSecureNoteCommand;
import club.gclmit.securefox.application.dto.SecureNoteDTO;
import club.gclmit.securefox.application.service.SecureNoteApplicationService;
import club.gclmit.securefox.domain.note.NoteType;
import club.gclmit.securefox.interfaces.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 安全笔记控制器
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/notes")
public class SecureNoteController {
    
    private final SecureNoteApplicationService noteApplicationService;
    
    public SecureNoteController(SecureNoteApplicationService noteApplicationService) {
        this.noteApplicationService = noteApplicationService;
    }
    
    /**
     * 创建安全笔记
     */
    @PostMapping
    public ApiResponse<SecureNoteDTO> createNote(@Valid @RequestBody CreateSecureNoteCommand command) {
        SecureNoteDTO note = noteApplicationService.createNote(command);
        return ApiResponse.success("安全笔记创建成功", note);
    }
    
    /**
     * 获取安全笔记列表
     */
    @GetMapping
    public ApiResponse<List<SecureNoteDTO>> getNotes() {
        List<SecureNoteDTO> notes = noteApplicationService.getUserNotes();
        return ApiResponse.success(notes);
    }
    
    /**
     * 根据类型获取安全笔记
     */
    @GetMapping("/type/{noteType}")
    public ApiResponse<List<SecureNoteDTO>> getNotesByType(@PathVariable NoteType noteType) {
        List<SecureNoteDTO> notes = noteApplicationService.getNotesByType(noteType);
        return ApiResponse.success(notes);
    }
    
    /**
     * 获取安全笔记详情
     */
    @GetMapping("/{id}")
    public ApiResponse<SecureNoteDTO> getNote(@PathVariable Long id) {
        SecureNoteDTO note = noteApplicationService.getNote(id);
        return ApiResponse.success(note);
    }
    
    /**
     * 获取安全笔记完整内容
     */
    @GetMapping("/{id}/content")
    public ApiResponse<String> getNoteContent(@PathVariable Long id) {
        String content = noteApplicationService.getNoteContent(id);
        return ApiResponse.success(content);
    }
    
    /**
     * 更新安全笔记
     */
    @PutMapping("/{id}")
    public ApiResponse<SecureNoteDTO> updateNote(@PathVariable Long id, 
                                                @Valid @RequestBody CreateSecureNoteCommand command) {
        SecureNoteDTO note = noteApplicationService.updateNote(id, command);
        return ApiResponse.success("安全笔记更新成功", note);
    }
    
    /**
     * 删除安全笔记
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteNote(@PathVariable Long id) {
        noteApplicationService.deleteNote(id);
        return ApiResponse.successMessage("安全笔记删除成功");
    }
    
    /**
     * 搜索安全笔记
     */
    @GetMapping("/search")
    public ApiResponse<List<SecureNoteDTO>> searchNotes(@RequestParam String keyword) {
        List<SecureNoteDTO> notes = noteApplicationService.searchNotes(keyword);
        return ApiResponse.success(notes);
    }
    
    /**
     * 设置收藏状态
     */
    @PutMapping("/{id}/favorite")
    public ApiResponse<String> setFavorite(@PathVariable Long id, @RequestBody FavoriteRequest request) {
        boolean success = noteApplicationService.setFavorite(id, request.isFavorite());
        if (success) {
            return ApiResponse.successMessage(request.isFavorite() ? "已添加到收藏" : "已取消收藏");
        } else {
            return ApiResponse.badRequestWithType("操作失败");
        }
    }
    
    /**
     * 设置置顶状态
     */
    @PutMapping("/{id}/pinned")
    public ApiResponse<String> setPinned(@PathVariable Long id, @RequestBody PinnedRequest request) {
        boolean success = noteApplicationService.setPinned(id, request.isPinned());
        if (success) {
            return ApiResponse.successMessage(request.isPinned() ? "已置顶" : "已取消置顶");
        } else {
            return ApiResponse.badRequestWithType("操作失败");
        }
    }
    
    /**
     * 获取收藏的安全笔记
     */
    @GetMapping("/favorites")
    public ApiResponse<List<SecureNoteDTO>> getFavoriteNotes() {
        List<SecureNoteDTO> notes = noteApplicationService.getFavoriteNotes();
        return ApiResponse.success(notes);
    }
    
    /**
     * 获取置顶的安全笔记
     */
    @GetMapping("/pinned")
    public ApiResponse<List<SecureNoteDTO>> getPinnedNotes() {
        List<SecureNoteDTO> notes = noteApplicationService.getPinnedNotes();
        return ApiResponse.success(notes);
    }
    
    /**
     * 获取安全笔记统计
     */
    @GetMapping("/statistics")
    public ApiResponse<SecureNoteApplicationService.NoteStatistics> getStatistics() {
        SecureNoteApplicationService.NoteStatistics statistics = noteApplicationService.generateStatistics();
        return ApiResponse.success(statistics);
    }
    
    /**
     * 检查内容安全性
     */
    @PostMapping("/security-check")
    public ApiResponse<SecureNoteApplicationService.ContentSecurityCheck> checkContentSecurity(
            @RequestBody ContentSecurityRequest request) {
        SecureNoteApplicationService.ContentSecurityCheck result = 
            noteApplicationService.checkContentSecurity(request.getContent());
        return ApiResponse.success(result);
    }
    
    /**
     * 获取笔记类型列表
     */
    @GetMapping("/types")
    public ApiResponse<List<NoteTypeInfo>> getNoteTypes() {
        List<NoteTypeInfo> types = java.util.Arrays.stream(NoteType.values())
                .map(type -> new NoteTypeInfo(
                    type.name(),
                    type.getDescription(),
                    type.getIconName(),
                    type.getColorCode(),
                    type.isHighSensitive(),
                    type.isTechnical()
                ))
                .collect(java.util.stream.Collectors.toList());
        
        return ApiResponse.success(types);
    }
    
    /**
     * 收藏请求
     */
    public static class FavoriteRequest {
        private boolean favorite;
        
        public boolean isFavorite() { return favorite; }
        public void setFavorite(boolean favorite) { this.favorite = favorite; }
    }
    
    /**
     * 置顶请求
     */
    public static class PinnedRequest {
        private boolean pinned;
        
        public boolean isPinned() { return pinned; }
        public void setPinned(boolean pinned) { this.pinned = pinned; }
    }
    
    /**
     * 内容安全检查请求
     */
    public static class ContentSecurityRequest {
        private String content;
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }
    
    /**
     * 笔记类型信息
     */
    public static class NoteTypeInfo {
        private final String name;
        private final String description;
        private final String iconName;
        private final String colorCode;
        private final boolean highSensitive;
        private final boolean technical;
        
        public NoteTypeInfo(String name, String description, String iconName, String colorCode,
                           boolean highSensitive, boolean technical) {
            this.name = name;
            this.description = description;
            this.iconName = iconName;
            this.colorCode = colorCode;
            this.highSensitive = highSensitive;
            this.technical = technical;
        }
        
        public String getName() { return name; }
        public String getDescription() { return description; }
        public String getIconName() { return iconName; }
        public String getColorCode() { return colorCode; }
        public boolean isHighSensitive() { return highSensitive; }
        public boolean isTechnical() { return technical; }
    }
}
