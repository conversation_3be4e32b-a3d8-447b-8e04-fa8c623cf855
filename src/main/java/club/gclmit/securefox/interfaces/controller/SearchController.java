package club.gclmit.securefox.interfaces.controller;

import club.gclmit.securefox.application.dto.AdvancedSearchQuery;
import club.gclmit.securefox.application.dto.PasswordDTO;
import club.gclmit.securefox.application.dto.SearchResult;
import club.gclmit.securefox.application.dto.SecureNoteDTO;
import club.gclmit.securefox.application.service.AdvancedSearchService;
import club.gclmit.securefox.interfaces.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜索控制器
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@RestController
@RequestMapping("/api/v1/search")
@RequiredArgsConstructor
public class SearchController {
    
    private final AdvancedSearchService searchService;
    
    /**
     * 高级搜索密码
     */
    @PostMapping("/passwords")
    public ApiResponse<SearchResult<PasswordDTO>> searchPasswords(
            @Valid @RequestBody AdvancedPasswordSearchRequest request) {
        
        AdvancedSearchQuery query = buildPasswordSearchQuery(request);
        SearchResult<PasswordDTO> result = searchService.searchPasswords(query);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 高级搜索笔记
     */
    @PostMapping("/notes")
    public ApiResponse<SearchResult<SecureNoteDTO>> searchNotes(
            @Valid @RequestBody AdvancedNoteSearchRequest request) {
        
        AdvancedSearchQuery query = buildNoteSearchQuery(request);
        SearchResult<SecureNoteDTO> result = searchService.searchNotes(query);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 快速搜索（同时搜索密码和笔记）
     */
    @GetMapping("/quick")
    public ApiResponse<QuickSearchResult> quickSearch(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") int limit) {
        
        // 搜索密码
        AdvancedSearchQuery passwordQuery = AdvancedSearchQuery.builder()
            .keyword(keyword)
            .searchType(AdvancedSearchQuery.SearchType.PASSWORD)
            .pageRequest(new AdvancedSearchQuery.PageRequest(1, limit))
            .build();
        
        SearchResult<PasswordDTO> passwordResult = searchService.searchPasswords(passwordQuery);
        
        // 搜索笔记
        AdvancedSearchQuery noteQuery = AdvancedSearchQuery.builder()
            .keyword(keyword)
            .searchType(AdvancedSearchQuery.SearchType.NOTE)
            .pageRequest(new AdvancedSearchQuery.PageRequest(1, limit))
            .build();
        
        SearchResult<SecureNoteDTO> noteResult = searchService.searchNotes(noteQuery);
        
        QuickSearchResult result = new QuickSearchResult();
        result.setKeyword(keyword);
        result.setPasswords(passwordResult.getItems());
        result.setNotes(noteResult.getItems());
        result.setTotalPasswordCount(passwordResult.getTotalCount());
        result.setTotalNoteCount(noteResult.getTotalCount());
        result.setSearchTimeMs(passwordResult.getSearchTimeMs() + noteResult.getSearchTimeMs());
        
        return ApiResponse.success(result);
    }
    
    /**
     * 获取搜索建议
     */
    @GetMapping("/suggestions")
    public ApiResponse<List<SearchResult.SearchSuggestion>> getSearchSuggestions(
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<SearchResult.SearchSuggestion> suggestions = searchService.getSearchSuggestions(query, null);
        
        return ApiResponse.success(suggestions.stream().limit(limit).toList());
    }
    
    /**
     * 重建搜索索引
     */
    @PostMapping("/rebuild-index")
    public ApiResponse<String> rebuildSearchIndex() {
        searchService.rebuildSearchIndex(null);
        return ApiResponse.success("搜索索引重建已开始");
    }
    
    // 辅助方法
    
    private AdvancedSearchQuery buildPasswordSearchQuery(AdvancedPasswordSearchRequest request) {
        return AdvancedSearchQuery.builder()
            .keyword(request.getKeyword())
            .searchType(AdvancedSearchQuery.SearchType.PASSWORD)
            .categoryIds(request.getCategoryIds())
            .websites(request.getWebsites())
            .hasTotp(request.getHasTotp())
            .strengthRange(request.getStrengthRange())
            .dateRange(request.getDateRange())
            .updateDateRange(request.getUpdateDateRange())
            .sortField(request.getSortField())
            .sortDirection(request.getSortDirection())
            .pageRequest(request.getPageRequest())
            .includeDeleted(request.isIncludeDeleted())
            .build();
    }
    
    private AdvancedSearchQuery buildNoteSearchQuery(AdvancedNoteSearchRequest request) {
        return AdvancedSearchQuery.builder()
            .keyword(request.getKeyword())
            .searchType(AdvancedSearchQuery.SearchType.NOTE)
            .categoryIds(request.getCategoryIds())
            .tags(request.getTags())
            .noteTypes(request.getNoteTypes())
            .favorite(request.getFavorite())
            .pinned(request.getPinned())
            .dateRange(request.getDateRange())
            .updateDateRange(request.getUpdateDateRange())
            .sortField(request.getSortField())
            .sortDirection(request.getSortDirection())
            .pageRequest(request.getPageRequest())
            .includeDeleted(request.isIncludeDeleted())
            .build();
    }
    
    // 请求类定义
    
    public static class AdvancedPasswordSearchRequest {
        private String keyword;
        private List<Long> categoryIds;
        private List<String> websites;
        private Boolean hasTotp;
        private AdvancedSearchQuery.PasswordStrengthRange strengthRange;
        private AdvancedSearchQuery.DateRange dateRange;
        private AdvancedSearchQuery.DateRange updateDateRange;
        private AdvancedSearchQuery.SortField sortField = AdvancedSearchQuery.SortField.UPDATED_AT;
        private AdvancedSearchQuery.SortDirection sortDirection = AdvancedSearchQuery.SortDirection.DESC;
        private AdvancedSearchQuery.PageRequest pageRequest = new AdvancedSearchQuery.PageRequest(1, 20);
        private boolean includeDeleted = false;
        
        // Getters and Setters
        public String getKeyword() { return keyword; }
        public void setKeyword(String keyword) { this.keyword = keyword; }
        public List<Long> getCategoryIds() { return categoryIds; }
        public void setCategoryIds(List<Long> categoryIds) { this.categoryIds = categoryIds; }
        public List<String> getWebsites() { return websites; }
        public void setWebsites(List<String> websites) { this.websites = websites; }
        public Boolean getHasTotp() { return hasTotp; }
        public void setHasTotp(Boolean hasTotp) { this.hasTotp = hasTotp; }
        public AdvancedSearchQuery.PasswordStrengthRange getStrengthRange() { return strengthRange; }
        public void setStrengthRange(AdvancedSearchQuery.PasswordStrengthRange strengthRange) { this.strengthRange = strengthRange; }
        public AdvancedSearchQuery.DateRange getDateRange() { return dateRange; }
        public void setDateRange(AdvancedSearchQuery.DateRange dateRange) { this.dateRange = dateRange; }
        public AdvancedSearchQuery.DateRange getUpdateDateRange() { return updateDateRange; }
        public void setUpdateDateRange(AdvancedSearchQuery.DateRange updateDateRange) { this.updateDateRange = updateDateRange; }
        public AdvancedSearchQuery.SortField getSortField() { return sortField; }
        public void setSortField(AdvancedSearchQuery.SortField sortField) { this.sortField = sortField; }
        public AdvancedSearchQuery.SortDirection getSortDirection() { return sortDirection; }
        public void setSortDirection(AdvancedSearchQuery.SortDirection sortDirection) { this.sortDirection = sortDirection; }
        public AdvancedSearchQuery.PageRequest getPageRequest() { return pageRequest; }
        public void setPageRequest(AdvancedSearchQuery.PageRequest pageRequest) { this.pageRequest = pageRequest; }
        public boolean isIncludeDeleted() { return includeDeleted; }
        public void setIncludeDeleted(boolean includeDeleted) { this.includeDeleted = includeDeleted; }
    }
    
    public static class AdvancedNoteSearchRequest {
        private String keyword;
        private List<Long> categoryIds;
        private List<String> tags;
        private List<String> noteTypes;
        private Boolean favorite;
        private Boolean pinned;
        private AdvancedSearchQuery.DateRange dateRange;
        private AdvancedSearchQuery.DateRange updateDateRange;
        private AdvancedSearchQuery.SortField sortField = AdvancedSearchQuery.SortField.UPDATED_AT;
        private AdvancedSearchQuery.SortDirection sortDirection = AdvancedSearchQuery.SortDirection.DESC;
        private AdvancedSearchQuery.PageRequest pageRequest = new AdvancedSearchQuery.PageRequest(1, 20);
        private boolean includeDeleted = false;
        
        // Getters and Setters
        public String getKeyword() { return keyword; }
        public void setKeyword(String keyword) { this.keyword = keyword; }
        public List<Long> getCategoryIds() { return categoryIds; }
        public void setCategoryIds(List<Long> categoryIds) { this.categoryIds = categoryIds; }
        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }
        public List<String> getNoteTypes() { return noteTypes; }
        public void setNoteTypes(List<String> noteTypes) { this.noteTypes = noteTypes; }
        public Boolean getFavorite() { return favorite; }
        public void setFavorite(Boolean favorite) { this.favorite = favorite; }
        public Boolean getPinned() { return pinned; }
        public void setPinned(Boolean pinned) { this.pinned = pinned; }
        public AdvancedSearchQuery.DateRange getDateRange() { return dateRange; }
        public void setDateRange(AdvancedSearchQuery.DateRange dateRange) { this.dateRange = dateRange; }
        public AdvancedSearchQuery.DateRange getUpdateDateRange() { return updateDateRange; }
        public void setUpdateDateRange(AdvancedSearchQuery.DateRange updateDateRange) { this.updateDateRange = updateDateRange; }
        public AdvancedSearchQuery.SortField getSortField() { return sortField; }
        public void setSortField(AdvancedSearchQuery.SortField sortField) { this.sortField = sortField; }
        public AdvancedSearchQuery.SortDirection getSortDirection() { return sortDirection; }
        public void setSortDirection(AdvancedSearchQuery.SortDirection sortDirection) { this.sortDirection = sortDirection; }
        public AdvancedSearchQuery.PageRequest getPageRequest() { return pageRequest; }
        public void setPageRequest(AdvancedSearchQuery.PageRequest pageRequest) { this.pageRequest = pageRequest; }
        public boolean isIncludeDeleted() { return includeDeleted; }
        public void setIncludeDeleted(boolean includeDeleted) { this.includeDeleted = includeDeleted; }
    }
    
    public static class QuickSearchResult {
        private String keyword;
        private List<PasswordDTO> passwords;
        private List<SecureNoteDTO> notes;
        private long totalPasswordCount;
        private long totalNoteCount;
        private long searchTimeMs;
        
        // Getters and Setters
        public String getKeyword() { return keyword; }
        public void setKeyword(String keyword) { this.keyword = keyword; }
        public List<PasswordDTO> getPasswords() { return passwords; }
        public void setPasswords(List<PasswordDTO> passwords) { this.passwords = passwords; }
        public List<SecureNoteDTO> getNotes() { return notes; }
        public void setNotes(List<SecureNoteDTO> notes) { this.notes = notes; }
        public long getTotalPasswordCount() { return totalPasswordCount; }
        public void setTotalPasswordCount(long totalPasswordCount) { this.totalPasswordCount = totalPasswordCount; }
        public long getTotalNoteCount() { return totalNoteCount; }
        public void setTotalNoteCount(long totalNoteCount) { this.totalNoteCount = totalNoteCount; }
        public long getSearchTimeMs() { return searchTimeMs; }
        public void setSearchTimeMs(long searchTimeMs) { this.searchTimeMs = searchTimeMs; }
    }
}
