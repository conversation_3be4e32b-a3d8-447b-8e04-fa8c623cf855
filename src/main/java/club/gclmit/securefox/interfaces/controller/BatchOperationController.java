package club.gclmit.securefox.interfaces.controller;

import club.gclmit.securefox.application.command.BatchOperationCommand;
import club.gclmit.securefox.application.dto.BatchOperationResult;
import club.gclmit.securefox.application.service.BatchOperationService;
import club.gclmit.securefox.interfaces.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 批量操作控制器
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2.0.0
 */
@RestController
@RequestMapping("/api/v1/batch")
@RequiredArgsConstructor
public class BatchOperationController {
    
    private final BatchOperationService batchOperationService;
    
    /**
     * 执行批量操作
     */
    @PostMapping("/execute")
    public ApiResponse<BatchOperationResult> executeBatchOperation(
            @Valid @RequestBody BatchOperationCommand command) {
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量删除密码
     */
    @PostMapping("/passwords/delete")
    public ApiResponse<BatchOperationResult> batchDeletePasswords(
            @RequestBody BatchDeleteRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.DELETE)
            .resourceType(BatchOperationCommand.ResourceType.PASSWORD)
            .resourceIds(request.getPasswordIds())
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量更新密码分类
     */
    @PostMapping("/passwords/update-category")
    public ApiResponse<BatchOperationResult> batchUpdatePasswordCategory(
            @RequestBody BatchUpdateCategoryRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.UPDATE_CATEGORY)
            .resourceType(BatchOperationCommand.ResourceType.PASSWORD)
            .resourceIds(request.getPasswordIds())
            .parameters(Map.of("categoryId", request.getCategoryId()))
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量检查密码重复
     */
    @PostMapping("/passwords/duplicate-check")
    public ApiResponse<BatchOperationResult> batchCheckPasswordDuplicates(
            @RequestBody BatchCheckRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.DUPLICATE_CHECK)
            .resourceType(BatchOperationCommand.ResourceType.PASSWORD)
            .resourceIds(request.getPasswordIds())
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量检查密码强度
     */
    @PostMapping("/passwords/strength-check")
    public ApiResponse<BatchOperationResult> batchCheckPasswordStrength(
            @RequestBody BatchCheckRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.STRENGTH_CHECK)
            .resourceType(BatchOperationCommand.ResourceType.PASSWORD)
            .resourceIds(request.getPasswordIds())
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量删除笔记
     */
    @PostMapping("/notes/delete")
    public ApiResponse<BatchOperationResult> batchDeleteNotes(
            @RequestBody BatchDeleteNotesRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.DELETE)
            .resourceType(BatchOperationCommand.ResourceType.NOTE)
            .resourceIds(request.getNoteIds())
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量更新笔记标签
     */
    @PostMapping("/notes/update-tags")
    public ApiResponse<BatchOperationResult> batchUpdateNoteTags(
            @RequestBody BatchUpdateNoteTagsRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.UPDATE_TAGS)
            .resourceType(BatchOperationCommand.ResourceType.NOTE)
            .resourceIds(request.getNoteIds())
            .parameters(Map.of("tags", request.getTags()))
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量设置笔记收藏状态
     */
    @PostMapping("/notes/set-favorite")
    public ApiResponse<BatchOperationResult> batchSetNoteFavorite(
            @RequestBody BatchSetNoteFavoriteRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.SET_FAVORITE)
            .resourceType(BatchOperationCommand.ResourceType.NOTE)
            .resourceIds(request.getNoteIds())
            .parameters(Map.of("favorite", request.isFavorite()))
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 批量设置笔记置顶状态
     */
    @PostMapping("/notes/set-pinned")
    public ApiResponse<BatchOperationResult> batchSetNotePinned(
            @RequestBody BatchSetNotePinnedRequest request) {
        
        BatchOperationCommand command = BatchOperationCommand.builder()
            .operation(BatchOperationCommand.BatchOperationType.SET_PINNED)
            .resourceType(BatchOperationCommand.ResourceType.NOTE)
            .resourceIds(request.getNoteIds())
            .parameters(Map.of("pinned", request.isPinned()))
            .async(request.isAsync())
            .build();
        
        BatchOperationResult result = batchOperationService.executeBatchOperation(command);
        return ApiResponse.success(result);
    }
    
    /**
     * 获取批量操作结果
     */
    @GetMapping("/result/{operationId}")
    public ApiResponse<BatchOperationResult> getBatchOperationResult(
            @PathVariable String operationId) {
        BatchOperationResult result = batchOperationService.getBatchOperationResult(operationId);
        if (result == null) {
            return ApiResponse.badRequestWithType("操作结果不存在或已过期");
        }
        return ApiResponse.success(result);
    }
    
    /**
     * 清理批量操作结果
     */
    @DeleteMapping("/result/{operationId}")
    public ApiResponse<String> cleanupBatchOperationResult(
            @PathVariable String operationId) {
        batchOperationService.cleanupOperationResult(operationId);
        return ApiResponse.success("操作结果已清理");
    }
    
    // 请求类定义
    
    public static class BatchDeleteRequest {
        private List<Long> passwordIds;
        private boolean async = true;
        
        public List<Long> getPasswordIds() { return passwordIds; }
        public void setPasswordIds(List<Long> passwordIds) { this.passwordIds = passwordIds; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
    
    public static class BatchUpdateCategoryRequest {
        private List<Long> passwordIds;
        private Long categoryId;
        private boolean async = true;
        
        public List<Long> getPasswordIds() { return passwordIds; }
        public void setPasswordIds(List<Long> passwordIds) { this.passwordIds = passwordIds; }
        public Long getCategoryId() { return categoryId; }
        public void setCategoryId(Long categoryId) { this.categoryId = categoryId; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
    
    public static class BatchCheckRequest {
        private List<Long> passwordIds;
        private boolean async = true;
        
        public List<Long> getPasswordIds() { return passwordIds; }
        public void setPasswordIds(List<Long> passwordIds) { this.passwordIds = passwordIds; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
    
    public static class BatchDeleteNotesRequest {
        private List<Long> noteIds;
        private boolean async = true;
        
        public List<Long> getNoteIds() { return noteIds; }
        public void setNoteIds(List<Long> noteIds) { this.noteIds = noteIds; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
    
    public static class BatchUpdateNoteTagsRequest {
        private List<Long> noteIds;
        private String tags;
        private boolean async = true;
        
        public List<Long> getNoteIds() { return noteIds; }
        public void setNoteIds(List<Long> noteIds) { this.noteIds = noteIds; }
        public String getTags() { return tags; }
        public void setTags(String tags) { this.tags = tags; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
    
    public static class BatchSetNoteFavoriteRequest {
        private List<Long> noteIds;
        private boolean favorite;
        private boolean async = true;
        
        public List<Long> getNoteIds() { return noteIds; }
        public void setNoteIds(List<Long> noteIds) { this.noteIds = noteIds; }
        public boolean isFavorite() { return favorite; }
        public void setFavorite(boolean favorite) { this.favorite = favorite; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
    
    public static class BatchSetNotePinnedRequest {
        private List<Long> noteIds;
        private boolean pinned;
        private boolean async = true;
        
        public List<Long> getNoteIds() { return noteIds; }
        public void setNoteIds(List<Long> noteIds) { this.noteIds = noteIds; }
        public boolean isPinned() { return pinned; }
        public void setPinned(boolean pinned) { this.pinned = pinned; }
        public boolean isAsync() { return async; }
        public void setAsync(boolean async) { this.async = async; }
    }
}
