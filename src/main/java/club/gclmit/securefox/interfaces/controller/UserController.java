package club.gclmit.securefox.interfaces.controller;

import club.gclmit.securefox.application.command.LoginCommand;
import club.gclmit.securefox.application.command.RegisterUserCommand;
import club.gclmit.securefox.application.dto.UserDTO;
import club.gclmit.securefox.application.service.UserApplicationService;
import club.gclmit.securefox.interfaces.dto.ApiResponse;

import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户管理控制器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/users")
public class UserController {
    
    private final UserApplicationService userApplicationService;
    
    public UserController(UserApplicationService userApplicationService) {
        this.userApplicationService = userApplicationService;
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<UserDTO> register(@Valid @RequestBody RegisterUserCommand command) {
        UserDTO user = userApplicationService.register(command);
        return ApiResponse.success("注册成功", user);
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<UserApplicationService.LoginResult> login(@Valid @RequestBody LoginCommand command) {
        UserApplicationService.LoginResult result = userApplicationService.login(command);
        return ApiResponse.success("登录成功", result);
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<String> logout() {
        userApplicationService.logout();
        return ApiResponse.successMessage("登出成功");
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public ApiResponse<UserDTO> getProfile() {
        UserDTO user = userApplicationService.getCurrentUser();
        return ApiResponse.success(user);
    }
    
    /**
     * 更改主密码
     */
    @PutMapping("/password")
    public ApiResponse<String> changePassword(@RequestBody ChangePasswordRequest request) {
        boolean success = userApplicationService.changeMasterPassword(
            request.getOldPassword(),
            request.getNewPassword()
        );

        if (success) {
            return ApiResponse.successMessage("密码修改成功");
        } else {
            return ApiResponse.badRequestWithType("原密码错误");
        }
    }
    
    /**
     * 检查密码强度
     */
    @PostMapping("/password/strength")
    public ApiResponse<UserApplicationService.PasswordStrengthInfo> checkPasswordStrength(
            @RequestBody PasswordStrengthRequest request) {
        UserApplicationService.PasswordStrengthInfo info = 
            userApplicationService.checkPasswordStrength(request.getPassword());
        return ApiResponse.success(info);
    }
    
    /**
     * 更改密码请求
     */
    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;
        
        public String getOldPassword() { return oldPassword; }
        public void setOldPassword(String oldPassword) { this.oldPassword = oldPassword; }
        public String getNewPassword() { return newPassword; }
        public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
    }
    
    /**
     * 密码强度检查请求
     */
    public static class PasswordStrengthRequest {
        private String password;
        
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }
}
