package club.gclmit.securefox.interfaces.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应格式
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 成功响应
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .code(200)
                .message("操作成功")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 成功响应（无数据）
     * 
     * @return API响应
     */
    public static ApiResponse<Void> success() {
        return success(null);
    }
    
    /**
     * 成功响应（自定义消息）
     *
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .code(200)
                .message(message)
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 成功响应（仅消息，无数据）
     *
     * @param message 响应消息
     * @return API响应
     */
    public static ApiResponse<String> successMessage(String message) {
        return ApiResponse.<String>builder()
                .code(200)
                .message("操作成功")
                .data(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 失败响应
     *
     * @param code 错误码
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> error(Integer code, String message) {
        return ApiResponse.<Void>builder()
                .code(code)
                .message(message)
                .data(null)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 失败响应（默认500错误）
     *
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> error(String message) {
        return error(500, message);
    }

    /**
     * 参数错误响应
     *
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> badRequest(String message) {
        return error(400, message);
    }

    /**
     * 参数错误响应（带数据类型）
     *
     * @param message 错误消息
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> badRequestWithType(String message) {
        return ApiResponse.<T>builder()
                .code(400)
                .message(message)
                .data(null)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 未授权响应
     * 
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> unauthorized(String message) {
        return error(401, message);
    }
    
    /**
     * 禁止访问响应
     * 
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> forbidden(String message) {
        return error(403, message);
    }
    
    /**
     * 资源不存在响应
     * 
     * @param message 错误消息
     * @return API响应
     */
    public static ApiResponse<Void> notFound(String message) {
        return error(404, message);
    }
}
